# Этот код предназначен для файла utils.py

import re
import html
import os
import sys
import subprocess
import traceback
import io
import time
import base64
import platform
import asyncio
import psutil  # For resource monitoring
import telebot # Added for specific exception handling
from telebot import types  # Added for ReactionTypeEmoji
try:
    from telebot.types import ReactionTypeEmoji
except ImportError:
    # Fallback for older versions of pyTelegramBotAPI
    ReactionTypeEmoji = None
import threading # Added for TypingStatusManager
import json
from datetime import datetime
import pytz
import queue  # For FFmpeg queue
from concurrent.futures import ProcessPoolExecutor

from bot_globals import log_admin
from config import TELEGRAM_MSG_LIMIT

# --- ProcessPool для FFmpeg операций ---
ffmpeg_pool = ProcessPoolExecutor(max_workers=2)

def run_ffmpeg_async(cmd, timeout=300):
    """Запускает FFmpeg в отдельном процессе для избежания блокировки GIL с timeout"""
    try:
        return subprocess.run(cmd, check=True, capture_output=True, text=True, timeout=timeout)
    except subprocess.TimeoutExpired:
        log_admin(f"FFmpeg process timed out after {timeout} seconds", level="error")
        raise
    except Exception as e:
        log_admin(f"FFmpeg process failed: {e}", level="error")
        raise





# Thread-safe bot API wrapper
_bot_api_lock = threading.RLock()

# FFmpeg queue system for sequential processing
ffmpeg_queue = queue.Queue()
ffmpeg_queue_lock = threading.Lock()
ffmpeg_queue_worker_running = False
ffmpeg_timeout = 300  # Default timeout for FFmpeg operations

def convert_markdown_to_html(text):
    """
    Converts markdown bold text (**text**) to HTML bold tags (<b>text</b>).
    Simple and fast implementation without regex for stability.
    """
    if not text or '**' not in text:
        return text

    result = []
    i = 0
    while i < len(text):
        if i < len(text) - 1 and text[i:i+2] == '**':
            # Find the closing **
            j = i + 2
            while j < len(text) - 1:
                if text[j:j+2] == '**':
                    # Found closing **, replace with HTML tags
                    bold_text = text[i+2:j]
                    if bold_text:  # Only if there's text between **
                        result.append(f'<b>{bold_text}</b>')
                        i = j + 2
                        break
                    else:
                        # Empty ** pair, just add as is
                        result.append('**')
                        i += 2
                        break
                j += 1
            else:
                # No closing ** found, add as is
                result.append('**')
                i += 2
        else:
            result.append(text[i])
            i += 1

    return ''.join(result)


def safe_bot_call(func, *args, **kwargs):
    """
    Thread-safe wrapper for bot API calls to prevent concurrent access issues.
    """
    with _bot_api_lock:
        try:
            return func(*args, **kwargs)
        except Exception as e:
            log_admin(f"Safe bot call error: {e}", level="error")
            raise

try:
    from PIL import Image
    PIL_SUPPORTED = True
except ImportError:
    PIL_SUPPORTED = False
    from bot_globals import log_admin as _log_admin_for_pil
    _log_admin_for_pil("Pillow (PIL) library not found. Image dimension detection will be unavailable.", level="info")

from bot_globals import message_states, message_states_lock, user_last_response, user_last_response_lock, bot
from config import SYSTEM_PROMPT_SUMMARIZE, MODEL_FLASH, MODEL_LITE_CLASSIFY_TRANSCRIBE, TELEGRAPH_SUPPORTED, TELEGRAPH_SHORT_NAME, TELEGRAPH_TOKEN_FILE, PDF_SUPPORTED, SUPPORTED_DOC_EXTENSIONS, MAX_AUDIO_DURATION, AUDIO_VIDEO_GROUP_DELAY, FORWARD_BATCH_DELAY, MAX_FORWARD_BATCH_SIZE, PROCESS_BUFFER_DELAY, MEDIA_GROUP_DELAY, SUMMARIZE_THRESHOLD, TELEGRAPH_THRESHOLD, STATUS_MESSAGES, SYSTEM_PROMPT_MAIN, SYSTEM_PROMPT_TRANSCRIBE, SYSTEM_PROMPT_FORMAT_TRANSCRIPT
from api_clients import call_llm, call_gemini_api

#     if not width or not height:
#         return "auto"
#     aspect_ratio = width / height
#     if aspect_ratio > 1.2:
#         return "1536x1024"
#     elif aspect_ratio < 0.8:
#         return "1024x1536"
#     else:
#         return "1024x1024"

def escape_html(text: str) -> str:
    if not isinstance(text, str): text = str(text)
    text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
    return text
def fix_telegram_html(text: str) -> str:
    if not isinstance(text, str):
        text = str(text)
    # Телеграм поддерживает только ограниченный набор тегов. Удаляем или
    # заменяем остальные, чтобы избежать ошибок парсинга.
    text = re.sub(r'<br\s*/?>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</?p[^>]*>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</?h[1-6][^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?div[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?span[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?(html|head|body)[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?table[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?tr[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'</?td[^>]*>', ' ', text, flags=re.IGNORECASE)
    text = re.sub(r'</?hr[^>]*>', '\n', text, flags=re.IGNORECASE)
    text = re.sub(r'</?(ul|ol)[^>]*>', '', text, flags=re.IGNORECASE)
    text = re.sub(r'<li[^>]*>', '\n- ', text, flags=re.IGNORECASE)
    text = re.sub(r'</li>', '', text, flags=re.IGNORECASE)

    # Дополнительные запрещенные теги для полной очистки
    text = re.sub(r'</?strong[^>]*>', '', text, flags=re.IGNORECASE)  # strong -> должно быть b
    text = re.sub(r'</?em[^>]*>', '', text, flags=re.IGNORECASE)      # em -> должно быть i
    text = re.sub(r'</?mark[^>]*>', '', text, flags=re.IGNORECASE)    # mark не поддерживается
    text = re.sub(r'</?del[^>]*>', '', text, flags=re.IGNORECASE)     # del -> должно быть s
    text = re.sub(r'</?ins[^>]*>', '', text, flags=re.IGNORECASE)     # ins не поддерживается
    text = re.sub(r'</?sup[^>]*>', '', text, flags=re.IGNORECASE)     # sup не поддерживается
    text = re.sub(r'</?sub[^>]*>', '', text, flags=re.IGNORECASE)     # sub не поддерживается
    text = re.sub(r'</?small[^>]*>', '', text, flags=re.IGNORECASE)   # small не поддерживается
    text = re.sub(r'</?big[^>]*>', '', text, flags=re.IGNORECASE)     # big не поддерживается
    text = re.sub(r'</?tt[^>]*>', '', text, flags=re.IGNORECASE)      # tt -> должно быть code
    text = re.sub(r'</?kbd[^>]*>', '', text, flags=re.IGNORECASE)     # kbd -> должно быть code
    text = re.sub(r'</?var[^>]*>', '', text, flags=re.IGNORECASE)     # var -> должно быть code
    text = re.sub(r'</?samp[^>]*>', '', text, flags=re.IGNORECASE)    # samp -> должно быть code
    text = re.sub(r'</?cite[^>]*>', '', text, flags=re.IGNORECASE)    # cite не поддерживается
    text = re.sub(r'</?abbr[^>]*>', '', text, flags=re.IGNORECASE)    # abbr не поддерживается
    text = re.sub(r'</?acronym[^>]*>', '', text, flags=re.IGNORECASE) # acronym не поддерживается
    text = re.sub(r'</?q[^>]*>', '', text, flags=re.IGNORECASE)       # q не поддерживается
    text = re.sub(r'</?blockquote[^>]*>', '', text, flags=re.IGNORECASE) # blockquote не поддерживается
    text = re.sub(r'</?center[^>]*>', '', text, flags=re.IGNORECASE)  # center не поддерживается
    text = re.sub(r'</?font[^>]*>', '', text, flags=re.IGNORECASE)    # font не поддерживается
    text = re.sub(r'</?strike[^>]*>', '', text, flags=re.IGNORECASE)  # strike -> должно быть s

    # Универсальная очистка: удаляем все оставшиеся HTML теги кроме разрешенных Telegram
    # Разрешенные теги: b, i, u, s, code, pre, a, tg-spoiler
    text = re.sub(r'<(?!/?(?:b|i|u|s|code|pre|a|tg-spoiler)\b)[^>]*>', '', text, flags=re.IGNORECASE)

    text = re.sub(r'\n{3,}', '\n\n', text)
    return text.strip()

def smart_split_html(text: str, max_length: int) -> list:
    """
    Intelligently splits HTML text into chunks while preserving tag structure.
    Each chunk will have properly closed tags and reopened tags as needed.

    Args:
        text: HTML text to split
        max_length: Maximum length for each chunk

    Returns:
        List of HTML chunks with proper tag structure
    """
    if not isinstance(text, str):
        text = str(text)

    if len(text) <= max_length:
        log_admin(f"Smart HTML split: Text length ({len(text)}) <= max_length ({max_length}), returning single chunk", level="debug")
        return [text]

    # Supported HTML tags by Telegram
    supported_tags = ['b', 'i', 'u', 's', 'code', 'pre', 'a', 'tg-spoiler']

    log_admin(f"Smart HTML split: Starting split of {len(text)} chars into chunks of max {max_length} chars", level="debug")

    chunks = []
    current_pos = 0

    while current_pos < len(text):
        # Calculate the end position for this chunk
        chunk_end = min(current_pos + max_length, len(text))

        # If this is the last chunk, take everything
        if chunk_end >= len(text):
            chunk = text[current_pos:]
            chunks.append(chunk)
            break

        # Find a safe place to split (avoid breaking HTML tags)
        safe_split_pos = find_safe_split_position(text, current_pos, chunk_end)

        log_admin(f"Smart HTML split: Chunk {len(chunks)+1}: pos {current_pos}-{safe_split_pos} (safe pos adjusted from {chunk_end})", level="debug")

        # Extract the chunk
        chunk = text[current_pos:safe_split_pos]

        # Get currently open tags at the split position
        open_tags = get_open_tags_at_position(text, safe_split_pos, supported_tags)

        if open_tags:
            log_admin(f"Smart HTML split: Found {len(open_tags)} open tags at split position: {open_tags}", level="debug")

        # Close open tags in current chunk
        chunk = close_open_tags(chunk, open_tags, supported_tags)

        chunks.append(chunk)

        # Move to next position
        current_pos = safe_split_pos

    # Reopen tags for subsequent chunks
    chunks = reopen_tags_in_chunks(chunks, supported_tags)

    log_admin(f"Smart HTML split: Completed. Created {len(chunks)} chunks with sizes: {[len(chunk) for chunk in chunks]}", level="debug")

    return chunks

def find_safe_split_position(text: str, start_pos: int, max_end_pos: int) -> int:
    """
    Finds a safe position to split HTML text, avoiding breaking tags.
    """
    # Start from the maximum position and work backwards
    for pos in range(max_end_pos, start_pos, -1):
        # Check if we're inside a tag
        if not is_inside_html_tag(text, pos):
            # Prefer splitting at whitespace or after punctuation
            if pos < len(text) and text[pos] in ' \n\t.,!?;:':
                return pos + 1
            # Or at the end of a tag
            elif pos > 0 and text[pos - 1] == '>':
                return pos

    # If no safe position found, split at max position but ensure we're not in a tag
    pos = max_end_pos
    while pos > start_pos and is_inside_html_tag(text, pos):
        pos -= 1

    return max(pos, start_pos + 1)

def is_inside_html_tag(text: str, position: int) -> bool:
    """
    Checks if the given position is inside an HTML tag.
    """
    # Look backwards for the nearest < or >
    last_open = text.rfind('<', 0, position)
    last_close = text.rfind('>', 0, position)

    # If we found a < after the last >, we're inside a tag
    return last_open > last_close

def get_open_tags_at_position(text: str, position: int, supported_tags: list) -> list:
    """
    Gets the list of HTML tags that are open at the given position.
    """
    open_tags = []
    text_before = text[:position]

    for tag in supported_tags:
        # Count opening and closing tags before this position
        opening_pattern = rf'<{tag}(?:\s[^>]*)?>'
        closing_pattern = rf'</{tag}>'

        opening_tags = re.findall(opening_pattern, text_before, re.IGNORECASE)
        closing_tags = re.findall(closing_pattern, text_before, re.IGNORECASE)

        # If more opening than closing, this tag is open
        open_count = len(opening_tags) - len(closing_tags)
        if open_count > 0:
            # For tags like <a> that can have attributes, we need to preserve them
            if tag == 'a':
                # Find the last opening <a> tag with its attributes
                last_a_match = None
                for match in re.finditer(opening_pattern, text_before, re.IGNORECASE):
                    last_a_match = match
                if last_a_match:
                    open_tags.append(last_a_match.group(0))
            else:
                open_tags.extend([f'<{tag}>'] * open_count)

    return open_tags

def close_open_tags(chunk: str, open_tags: list, supported_tags: list) -> str:
    """
    Closes open tags at the end of a chunk.
    """
    if not open_tags:
        return chunk

    # Close tags in reverse order (LIFO)
    closing_tags = []
    for tag_with_attrs in reversed(open_tags):
        # Extract tag name from tag with attributes
        tag_match = re.match(r'<(\w+)', tag_with_attrs)
        if tag_match:
            tag_name = tag_match.group(1)
            closing_tags.append(f'</{tag_name}>')

    return chunk + ''.join(closing_tags)

def reopen_tags_in_chunks(chunks: list, supported_tags: list) -> list:
    """
    Reopens necessary tags at the beginning of subsequent chunks.
    """
    if len(chunks) <= 1:
        return chunks

    result_chunks = [chunks[0]]

    for i in range(1, len(chunks)):
        # Analyze what tags should be reopened based on the previous chunk
        prev_chunk = result_chunks[i - 1]
        current_chunk = chunks[i]

        # Find tags that were closed at the end of previous chunk
        tags_to_reopen = find_tags_to_reopen(prev_chunk, supported_tags)

        # Prepend opening tags to current chunk
        if tags_to_reopen:
            current_chunk = ''.join(tags_to_reopen) + current_chunk

        result_chunks.append(current_chunk)

    return result_chunks

def find_tags_to_reopen(chunk: str, supported_tags: list) -> list:
    """
    Finds which tags need to be reopened in the next chunk.
    """
    # This is a simplified approach - in a real implementation,
    # we would track the tag stack more carefully
    tags_to_reopen = []

    # Look for closing tags at the end of the chunk
    closing_tags_pattern = r'</(\w+)>(?:\s*</(\w+)>)*\s*$'
    match = re.search(closing_tags_pattern, chunk)

    if match:
        # For now, we'll use a simpler approach and let validate_and_fix_html_tags handle it
        pass

    return tags_to_reopen

def validate_and_fix_html_tags(text: str) -> str:
    """
    Validates and fixes HTML tags to ensure they are properly closed and supported by Telegram.
    Enhanced version that better handles broken tags from splitting.
    """
    if not isinstance(text, str):
        text = str(text)

    # Remove class attributes from code tags (Telegram doesn't support them)
    text = re.sub(r'<code\s+class="[^"]*">', '<code>', text, flags=re.IGNORECASE)

    # First, remove any broken/incomplete tags (tags that start but don't close properly)
    text = re.sub(r'<[^>]*$', '', text)  # Remove incomplete tag at end
    text = re.sub(r'^[^<]*>', '', text)  # Remove incomplete closing tag at start

    # List of supported tags by Telegram
    supported_tags = ['b', 'i', 'u', 's', 'code', 'pre', 'a', 'tg-spoiler']

    # Find all opening tags and ensure they have closing tags
    for tag in supported_tags:
        # Count opening and closing tags
        opening_pattern = rf'<{tag}(?:\s[^>]*)?>'
        closing_pattern = rf'</{tag}>'

        opening_tags = re.findall(opening_pattern, text, re.IGNORECASE)
        closing_tags = re.findall(closing_pattern, text, re.IGNORECASE)

        # If there are more opening tags than closing tags, add missing closing tags
        if len(opening_tags) > len(closing_tags):
            missing_closing = len(opening_tags) - len(closing_tags)
            text += f'</{tag}>' * missing_closing
            log_admin(f"Fixed {missing_closing} unclosed <{tag}> tags", level="warning")

        # If there are more closing tags than opening tags, remove extra closing tags
        elif len(closing_tags) > len(opening_tags):
            extra_closing = len(closing_tags) - len(opening_tags)
            # Remove extra closing tags from the beginning
            for _ in range(extra_closing):
                text = re.sub(rf'</{tag}>', '', text, count=1, flags=re.IGNORECASE)
            log_admin(f"Removed {extra_closing} extra </{tag}> closing tags", level="warning")

    # Remove any unsupported HTML tags but keep their content
    text = re.sub(r'<(?!/?(?:' + '|'.join(supported_tags) + r')\b)[^>]*>', '', text, flags=re.IGNORECASE)

    return text

def fix_telegram_markdown_v2(text: str) -> str:
    """Escape special characters for Telegram MarkdownV2 (see official docs)."""
    if not isinstance(text, str):
        text = str(text)
    # Экранируем все спецсимволы MarkdownV2
    to_escape = r"_*[]()~`>#+-=|{}.!"
    for ch in to_escape:
        text = text.replace(ch, '\\' + ch)
    return text

def replace_asterisks_with_bullets(text: str) -> str:
    """
    Заменяет одинарные звездочки (*) в начале строк или после пробелов на красивые точки (•).
    Убирает лишние пробелы после звездочки (оставляет только один).
    НЕ трогает двойные звездочки (**) для жирного форматирования.
    """
    if not isinstance(text, str):
        return ""

    # Обрабатываем одинарные звездочки в начале строк
    # Паттерн: начало строки + одна звездочка (НЕ за которой следует еще одна) + пробелы + текст
    text = re.sub(r'^(\*)(?!\*)\s*(?=\S)', '• ', text, flags=re.MULTILINE)

    # Обрабатываем одинарные звездочки после пробелов
    # Паттерн: пробел + одна звездочка (НЕ за которой следует еще одна) + пробелы + текст
    text = re.sub(r'(\s)(\*)(?!\*)\s*(?=\S)', r'\1• ', text)

    return text

def clean_response_text(text: str) -> str:
    """
    Очищает текст ответа от ИИ: удаляет символы "`" и "###", заменяет одинарные звездочки на красивые точки.
    AI models format text well enough without additional cleaning.
    """
    if not isinstance(text, str):
        return ""

    # Сначала очищаем текст
    cleaned_text = text.strip()

    # Удаляем символы "`" перед отправкой
    cleaned_text = cleaned_text.replace("`", "")

    # Удаляем символы "###" (три решетки) перед отправкой
    cleaned_text = cleaned_text.replace("###", "")

    # Затем заменяем одинарные звездочки на красивые точки
    cleaned_text = replace_asterisks_with_bullets(cleaned_text)

    return cleaned_text

def parse_file_tags(text: str) -> tuple[str, list[dict]]:
    """
    Парсит FILE теги из текста ответа ИИ.

    Формат тега: [FILE:имя_файла:расширение: содержимое ]

    Args:
        text (str): Текст ответа от ИИ

    Returns:
        tuple[str, list[dict]]: (очищенный_текст, список_файлов)
        где список_файлов содержит словари с ключами:
        - 'name': имя файла
        - 'extension': расширение файла
        - 'content': содержимое файла
    """
    import re

    if not isinstance(text, str):
        return text, []

    # Регулярное выражение для поиска FILE тегов
    # Ищем [FILE:имя:расширение: содержимое ]
    pattern = r'\[FILE:([^:]+):([^:]+):\s*(.*?)\s*\]'

    files_to_send = []
    cleaned_text = text

    # Находим все совпадения
    matches = re.finditer(pattern, text, re.DOTALL)

    for match in matches:
        filename = match.group(1).strip()
        extension = match.group(2).strip()
        content = match.group(3).strip()

        # Валидация имени файла (убираем опасные символы)
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        extension = re.sub(r'[<>:"/\\|?*]', '_', extension)

        # Добавляем файл в список
        files_to_send.append({
            'name': filename,
            'extension': extension,
            'content': content
        })

        # Удаляем тег из текста
        cleaned_text = cleaned_text.replace(match.group(0), '', 1)

    # Очищаем лишние пробелы и переносы строк
    cleaned_text = re.sub(r'\n{3,}', '\n\n', cleaned_text).strip()

    return cleaned_text, files_to_send

def extract_image_tool_call(text: str) -> tuple[str,str] | None:
    """
    Ищет в тексте конструкцию <image_gen prompt="…" size="…">
    и возвращает (prompt,size) либо None.
    Также пытается обработать другие возможные форматы, которые может использовать ИИ.
    """
    import re

    # Основной правильный формат
    m = re.search(r'<image_gen\s+prompt="([^"]+)"\s+size="([^"]+)"\s*>', text)
    if m:
        return m.group(1), m.group(2)

    # Альтернативный формат с одинарными кавычками
    m = re.search(r"<image_gen\s+prompt='([^']+)'\s+size='([^']+)'\s*>", text)
    if m:
        return m.group(1), m.group(2)

    # Формат без размера (используем размер по умолчанию)
    m = re.search(r'<image_gen\s+prompt="([^"]+)"\s*>', text)
    if m:
        return m.group(1), "1024x1024"

    # Попытка извлечь из неправильных форматов типа print(image_gen.generate_images(...))
    m = re.search(r'image_gen\.generate_images?\([^)]*prompts?\s*=\s*\["([^"]+)"\]', text)
    if m:
        log_admin(f"[ImageGen] Detected incorrect format, extracted prompt: {m.group(1)[:50]}...", level="warning")
        return m.group(1), "1024x1024"

    # Попытка извлечь из других неправильных форматов
    m = re.search(r'generate_images?\([^)]*"([^"]+)"', text)
    if m:
        log_admin(f"[ImageGen] Detected incorrect format, extracted prompt: {m.group(1)[:50]}...", level="warning")
        return m.group(1), "1024x1024"

    return None

def fix_image_gen_format(text: str) -> str:
    """
    Исправляет неправильные форматы генерации изображений в тексте ИИ,
    заменяя их на правильный XML формат.
    """
    import re

    # Исправляем print(image_gen.generate_images(...))
    def replace_print_format(match):
        prompt = match.group(1)
        return f'<image_gen prompt="{prompt}" size="1024x1024">'

    text = re.sub(r'print\(image_gen\.generate_images?\([^)]*prompts?\s*=\s*\["([^"]+)"\]',
                  replace_print_format, text)

    # Исправляем generate_images("...")
    def replace_generate_format(match):
        prompt = match.group(1)
        return f'<image_gen prompt="{prompt}" size="1024x1024">'

    text = re.sub(r'generate_images?\([^)]*"([^"]+)"',
                  replace_generate_format, text)

    # Убираем лишние print() вокруг правильных тегов
    text = re.sub(r'print\((<image_gen[^>]*>)\)', r'\1', text)

    return text

def convert_to_mp3(input_path, output_path):
    log_admin(f"Attempting to convert '{input_path}' to '{output_path}' using ffmpeg", level="debug")
    # Use consistent 44.1kHz sample rate for all audio to prevent playback speed issues
    # Add explicit format detection and normalization
    command = [
        'ffmpeg', '-i', input_path, '-vn',
        '-ar', '44100',  # Standard sample rate
        '-ac', '1',      # Mono
        '-codec:a', 'libmp3lame',
        '-b:a', '128k',  # Explicit bitrate
        '-q:a', '5',
        '-af', 'aresample=44100',  # Force resample to prevent speed issues
        '-y', output_path
    ]
    try:
        log_ffmpeg_operation_start("MP3 conversion", [input_path], output_path)
        success, result, error_msg = run_ffmpeg_with_monitoring(
            command, "MP3 conversion"
        )

        if success:
            log_admin(f"ffmpeg conversion successful for '{input_path}'. Output: {output_path}", level="info")
            return True
        else:
            log_admin(f"ffmpeg conversion failed for '{input_path}': {error_msg}", level="error")
            return False

    except FileNotFoundError:
        log_admin("ffmpeg command not found. Please ensure ffmpeg is installed and in the system path.", level="critical")
        return False
    except Exception as e:
        log_admin(f"Unexpected error during ffmpeg conversion for '{input_path}': {e}\n{traceback.format_exc()}", level="error")
        return False








def check_ffmpeg_availability():
    """
    Check if FFmpeg is available in the system.

    Returns:
        bool: True if FFmpeg is available, False otherwise
    """
    try:
        result = subprocess.run(['ffmpeg', '-version'],
                              capture_output=True,
                              text=True,
                              timeout=10)
        if result.returncode == 0:
            log_admin(f"FFmpeg is available: {result.stdout.split()[2] if len(result.stdout.split()) > 2 else 'version unknown'}", level="info")
            return True
        else:
            log_admin(f"FFmpeg check failed with return code {result.returncode}", level="error")
            return False
    except FileNotFoundError:
        log_admin("FFmpeg not found in system PATH", level="error")
        return False
    except subprocess.TimeoutExpired:
        log_admin("FFmpeg version check timed out", level="error")
        return False
    except Exception as e:
        log_admin(f"Error checking FFmpeg availability: {e}", level="error")
        return False


def check_system_resources():
    """
    Check system resources before starting FFmpeg operations.
    Uses server configuration for optimal thresholds.

    Returns:
        bool: True if resources are available, False if system is overloaded
    """
    try:
        # First check if FFmpeg is available
        if not check_ffmpeg_availability():
            log_admin("FFmpeg is not available, cannot proceed with audio operations", level="error")
            return False

        # Get server-specific thresholds
        try:
            from server_config import get_resource_thresholds
            thresholds = get_resource_thresholds()
        except ImportError:
            # More reasonable fallback values for better stability on loaded servers
            thresholds = {'cpu_critical': 98, 'memory_critical': 92}

        # Check CPU usage
        cpu_percent = psutil.cpu_percent(interval=0.5)
        if cpu_percent > thresholds['cpu_critical']:
            log_admin(f"Critical CPU usage detected: {cpu_percent}%. Delaying FFmpeg operation.")
            return False

        # Check memory usage
        memory = psutil.virtual_memory()
        if memory.percent > thresholds['memory_critical']:
            log_admin(f"Critical memory usage detected: {memory.percent}%. Delaying FFmpeg operation.")
            return False

        # Check available disk space
        disk = psutil.disk_usage('/')
        free_mb = disk.free / (1024**2)
        min_free_mb = 100  # Default minimum
        try:
            from server_config import get_server_config
            config = get_server_config()
            min_free_mb = config['resource_thresholds']['disk_critical_mb']
        except ImportError:
            pass

        if free_mb < min_free_mb:
            log_admin(f"Low disk space: {free_mb:.2f}MB free. Delaying FFmpeg operation.")
            return False

        log_admin(f"System resources OK: CPU {cpu_percent}%, Memory {memory.percent}%, Disk {free_mb:.2f}MB free")
        return True

    except Exception as e:
        log_admin(f"Error checking system resources: {e}. Proceeding with FFmpeg operation.")
        return True  # If we can't check, assume it's OK


def check_system_resources_with_retry(max_retries=8, wait_time=2, force_mode=False):
    """
    Check system resources with intelligent retry mechanism for low-resource servers.
    Enhanced with more retries and force mode for critical operations like audio combining.

    Args:
        max_retries: Maximum number of retry attempts (increased to 8 for better reliability)
        wait_time: Base wait time between retries (adaptive strategy)
        force_mode: If True, allows operation even under high load for critical tasks

    Returns:
        bool: True if resources become available, False if consistently overloaded
    """
    for attempt in range(max_retries + 1):
        resources_ok = check_system_resources()

        if resources_ok:
            if attempt > 0:
                log_admin(f"System resources became available after {attempt} retries")
            return True

        # Force mode allows critical operations (like audio combining) to proceed
        if force_mode and attempt >= max_retries // 2:
            log_admin(f"Force mode enabled: allowing critical operation despite resource constraints (attempt {attempt + 1})")
            return True

        if attempt < max_retries:
            # Adaptive backoff strategy: shorter waits initially, longer waits later
            if attempt < 3:
                current_wait = wait_time  # 2s for first 3 attempts
            elif attempt < 6:
                current_wait = wait_time * 2  # 4s for next 3 attempts
            else:
                current_wait = wait_time * 3  # 6s for final attempts

            log_admin(f"Resources unavailable, waiting {current_wait}s before retry {attempt + 1}/{max_retries}")
            time.sleep(current_wait)
        else:
            log_admin(f"System resources still unavailable after {max_retries} retries")

    return False


def start_ffmpeg_queue_worker():
    """
    Start the FFmpeg queue worker thread for processing operations sequentially.
    This helps prevent resource overload on low-resource servers.
    """
    global ffmpeg_queue_worker_running

    with ffmpeg_queue_lock:
        if ffmpeg_queue_worker_running:
            return  # Worker already running

        ffmpeg_queue_worker_running = True
        worker_thread = threading.Thread(target=_ffmpeg_queue_worker, daemon=True)
        worker_thread.start()
        log_admin("FFmpeg queue worker started", level="info")


def _ffmpeg_queue_worker():
    """
    Worker thread that processes FFmpeg operations from the queue.
    """
    global ffmpeg_queue_worker_running

    log_admin("FFmpeg queue worker thread started", level="info")

    while ffmpeg_queue_worker_running:
        try:
            # Get operation from queue with timeout
            operation = ffmpeg_queue.get(timeout=30)

            if operation is None:  # Shutdown signal
                break

            operation_type, func, args, kwargs, result_callback = operation
            log_admin(f"Processing queued FFmpeg operation: {operation_type}", level="info")

            try:
                # Wait for resources to be available with enhanced retry and force mode for critical operations
                force_mode = "audio" in operation_type.lower() or "combine" in operation_type.lower()
                if check_system_resources_with_retry(max_retries=8, wait_time=3, force_mode=force_mode):
                    result = func(*args, **kwargs)
                    if result_callback:
                        # For audio combination operations, result is output_path (success) or None (failure)
                        if "combine" in operation_type.lower() or "audio" in operation_type.lower():
                            if result is not None:
                                result_callback(True, result)  # result is the output_path
                            else:
                                result_callback(False, "Audio combination failed")
                        else:
                            # For other operations, use the original logic
                            result_callback(True, result)
                else:
                    log_admin(f"Skipping FFmpeg operation {operation_type} due to resource constraints after enhanced retries", level="warning")
                    if result_callback:
                        result_callback(False, "Resource constraints after enhanced retries")
            except Exception as e:
                log_admin(f"Error in queued FFmpeg operation {operation_type}: {e}", level="error")
                if result_callback:
                    result_callback(False, str(e))
            finally:
                ffmpeg_queue.task_done()

        except queue.Empty:
            continue  # No operations in queue, continue waiting
        except Exception as e:
            import traceback
            log_admin(f"Error in FFmpeg queue worker: {e}\n{traceback.format_exc()}", level="error")

    log_admin("FFmpeg queue worker thread stopped", level="info")


def queue_ffmpeg_operation(operation_type, func, args=(), kwargs=None, result_callback=None):
    """
    Queue an FFmpeg operation for sequential processing.

    Args:
        operation_type: Description of the operation
        func: Function to execute
        args: Function arguments
        kwargs: Function keyword arguments
        result_callback: Callback function to handle results (success, result)
    """
    if kwargs is None:
        kwargs = {}

    # Start worker if not running
    start_ffmpeg_queue_worker()

    # Add operation to queue
    operation = (operation_type, func, args, kwargs, result_callback)
    ffmpeg_queue.put(operation)
    log_admin(f"Queued FFmpeg operation: {operation_type} (queue size: {ffmpeg_queue.qsize()})", level="info")


def log_ffmpeg_operation_start(operation_type, input_files, output_file):
    """
    Log the start of an FFmpeg operation with detailed information.
    """
    log_admin(f"=== FFmpeg Operation Started ===")
    log_admin(f"Operation: {operation_type}")
    log_admin(f"Input files: {input_files}")
    log_admin(f"Output file: {output_file}")

    # Log current system resources
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        free_gb = disk.free / (1024**3)

        log_admin(f"System resources at start: CPU {cpu_percent}%, Memory {memory.percent}%, Disk {free_gb:.2f}GB free")
    except Exception as e:
        log_admin(f"Could not log system resources: {e}")


def log_ffmpeg_operation_end(operation_type, success, duration, error_msg=None):
    """
    Log the end of an FFmpeg operation with results.
    """
    status = "SUCCESS" if success else "FAILED"
    log_admin(f"=== FFmpeg Operation Ended ===")
    log_admin(f"Operation: {operation_type}")
    log_admin(f"Status: {status}")
    log_admin(f"Duration: {duration:.2f} seconds")

    if error_msg:
        log_admin(f"Error: {error_msg}")

    # Log final system resources
    try:
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        log_admin(f"System resources at end: CPU {cpu_percent}%, Memory {memory.percent}%")
    except Exception as e:
        log_admin(f"Could not log final system resources: {e}")

    log_admin("=== End FFmpeg Operation Log ===")


def safe_subprocess_run(cmd, timeout=60, operation_name="subprocess"):
    """
    Safely run subprocess command with crash protection.

    Args:
        cmd: Command list to execute
        timeout: Timeout in seconds
        operation_name: Name for logging

    Returns:
        tuple: (success: bool, returncode: int, stdout: str, stderr: str)
    """
    process = None
    try:
        log_admin(f"Starting safe subprocess: {operation_name}", level="debug")

        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            preexec_fn=None if sys.platform.startswith('win') else os.setsid
        )

        stdout, stderr = process.communicate(timeout=timeout)

        success = process.returncode == 0
        log_admin(f"Safe subprocess {operation_name} completed: success={success}, returncode={process.returncode}", level="debug")

        return success, process.returncode, stdout, stderr

    except subprocess.TimeoutExpired:
        log_admin(f"Safe subprocess {operation_name} timed out after {timeout}s", level="warning")

        if process:
            try:
                import signal
                if sys.platform.startswith('win'):
                    process.terminate()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=5)
            except Exception:
                try:
                    if sys.platform.startswith('win'):
                        process.kill()
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                except Exception:
                    pass

        return False, -1, "", f"Process timed out after {timeout} seconds"

    except Exception as e:
        log_admin(f"Safe subprocess {operation_name} failed with exception: {e}", level="error")

        if process:
            try:
                import signal
                if sys.platform.startswith('win'):
                    process.terminate()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=2)
            except Exception:
                try:
                    if sys.platform.startswith('win'):
                        process.kill()
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                except Exception:
                    pass

        return False, -1, "", str(e)


def cleanup_hanging_ffmpeg_processes(chat_id=None):
    """
    Clean up any hanging FFmpeg processes to prevent system crashes.

    Args:
        chat_id: Optional chat ID to filter processes for specific chat
    """
    try:
        import psutil
        import signal

        cleaned_count = 0
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                if proc.info['name'] == 'ffmpeg':
                    cmdline = proc.info['cmdline'] or []

                    # Check if this is a podcast-related FFmpeg process
                    is_podcast_ffmpeg = any('podcast_raw_' in str(arg) for arg in cmdline)

                    if is_podcast_ffmpeg:
                        # If chat_id is specified, only clean processes for that chat
                        if chat_id is not None:
                            if not any(f'podcast_raw_{chat_id}' in str(arg) for arg in cmdline):
                                continue

                        # Check if process is old (running for more than 15 minutes)
                        import time
                        process_age = time.time() - proc.info['create_time']
                        if process_age > 900:  # 15 minutes
                            log_admin(f"Terminating old FFmpeg process PID {proc.info['pid']} (age: {process_age:.1f}s)", level="warning")
                            proc.terminate()
                            cleaned_count += 1
                        elif chat_id is not None:
                            # If specific chat_id cleanup requested, terminate regardless of age
                            log_admin(f"Terminating FFmpeg process PID {proc.info['pid']} for chat {chat_id}", level="info")
                            proc.terminate()
                            cleaned_count += 1

            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
            except Exception as e:
                log_admin(f"Error checking FFmpeg process: {e}", level="warning")
                continue

        if cleaned_count > 0:
            log_admin(f"Cleaned up {cleaned_count} hanging FFmpeg processes", level="info")

    except Exception as e:
        log_admin(f"Error in cleanup_hanging_ffmpeg_processes: {e}", level="error")


def create_ffmpeg_command_with_priority(base_cmd):
    """
    Создает команду FFmpeg с низким приоритетом, кроссплатформенно.

    Args:
        base_cmd: Список с базовой командой FFmpeg (начинающийся с 'ffmpeg')

    Returns:
        Список команды с добавленным управлением приоритетом
    """
    system = platform.system().lower()

    if system in ['linux', 'darwin']:  # Linux или macOS
        # Используем nice для Unix-систем
        return ['nice', '-n', '10'] + base_cmd
    elif system == 'windows':
        # В Windows используем start с низким приоритетом
        # Но для subprocess лучше установить приоритет через creationflags
        return base_cmd
    else:
        # Для неизвестных систем просто возвращаем базовую команду
        log_admin(f"Unknown system: {system}, using default FFmpeg command", level="debug")
        return base_cmd


def run_ffmpeg_with_low_priority(cmd, **kwargs):
    """
    Запускает FFmpeg с низким приоритетом, кроссплатформенно.

    Args:
        cmd: Команда FFmpeg
        **kwargs: Дополнительные аргументы для subprocess.run

    Returns:
        Результат subprocess.run
    """
    system = platform.system().lower()

    if system == 'windows':
        # В Windows устанавливаем низкий приоритет через creationflags
        import subprocess
        kwargs['creationflags'] = subprocess.BELOW_NORMAL_PRIORITY_CLASS

    return subprocess.run(cmd, **kwargs)


def setup_process_limits():
    """
    Настройка ограничений процесса для экономии ресурсов.
    Устанавливает низкий приоритет и ограничения памяти.
    """
    try:
        import psutil

        # Получаем текущий процесс
        current_process = psutil.Process()

        # Устанавливаем низкий приоритет (nice value)
        try:
            if hasattr(os, 'nice'):
                os.nice(5)  # Увеличиваем nice value (снижаем приоритет)
                log_admin("Process priority lowered (nice +5)", level="debug")
        except Exception as e:
            log_admin(f"Could not set process priority: {e}", level="debug")

        # Ограничиваем использование памяти процессом
        try:
            # Устанавливаем мягкое ограничение памяти
            current_process.nice(psutil.BELOW_NORMAL_PRIORITY_CLASS if hasattr(psutil, 'BELOW_NORMAL_PRIORITY_CLASS') else 10)
            log_admin("Process priority class set to below normal", level="debug")
        except Exception as e:
            log_admin(f"Could not set process priority class: {e}", level="debug")

    except Exception as e:
        log_admin(f"Error setting up process limits: {e}", level="debug")


def run_ffmpeg_with_monitoring(cmd, operation_type, timeout=None):
    """
    Run FFmpeg command with detailed monitoring and error handling.

    Args:
        cmd: FFmpeg command list
        operation_type: Description of the operation
        timeout: Timeout in seconds (uses global ffmpeg_timeout if None)

    Returns:
        tuple: (success: bool, result: subprocess.CompletedProcess or None, error_msg: str or None)
    """
    if timeout is None:
        timeout = ffmpeg_timeout

    start_time = time.time()

    process = None
    try:
        log_admin(f"Running {operation_type} with timeout {timeout}s: {' '.join(cmd)}", level="info")

        # Check system resources before starting
        try:
            import psutil
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if memory.percent > 85:
                log_admin(f"High memory usage ({memory.percent}%) before FFmpeg {operation_type}", level="warning")
            if cpu_percent > 90:
                log_admin(f"High CPU usage ({cpu_percent}%) before FFmpeg {operation_type}", level="warning")
        except Exception:
            pass

        # Use Popen for better process control
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            preexec_fn=None if sys.platform.startswith('win') else os.setsid
        )

        # Wait with timeout
        stdout, stderr = process.communicate(timeout=timeout)
        duration = time.time() - start_time

        if process.returncode == 0:
            log_admin(f"FFmpeg SUCCESS: {operation_type} completed in {duration:.2f}s", level="info")
            if stdout:
                log_admin(f"FFmpeg stdout: {stdout[:500]}", level="debug")
            log_ffmpeg_operation_end(operation_type, True, duration)
            return True, process, None
        else:
            error_msg = f"FFmpeg failed with return code {process.returncode}: {stderr}"
            log_admin(f"FFmpeg FAILED: {operation_type} - {error_msg}", level="error")
            if stdout:
                log_admin(f"FFmpeg stdout: {stdout[:500]}", level="error")
            log_ffmpeg_operation_end(operation_type, False, duration, error_msg)
            return False, process, error_msg

    except subprocess.TimeoutExpired as e:
        duration = time.time() - start_time
        error_msg = f"FFmpeg operation timed out after {timeout} seconds"
        log_admin(f"FFmpeg TIMEOUT: {operation_type} - {error_msg}", level="error")

        # Cleanup hanging process
        if process:
            try:
                import signal
                if sys.platform.startswith('win'):
                    process.terminate()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=5)
            except Exception:
                try:
                    if sys.platform.startswith('win'):
                        process.kill()
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                except Exception:
                    pass

        log_ffmpeg_operation_end(operation_type, False, duration, error_msg)
        return False, None, error_msg

    except Exception as e:
        duration = time.time() - start_time
        error_msg = f"Unexpected error during FFmpeg operation: {e}"
        log_admin(f"FFmpeg EXCEPTION: {operation_type} - {error_msg}", level="error")
        log_admin(f"Exception traceback: {traceback.format_exc()}", level="error")

        # Cleanup hanging process
        if process:
            try:
                import signal
                if sys.platform.startswith('win'):
                    process.terminate()
                else:
                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=2)
            except Exception:
                try:
                    if sys.platform.startswith('win'):
                        process.kill()
                    else:
                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                except Exception:
                    pass

        log_ffmpeg_operation_end(operation_type, False, duration, error_msg)
        return False, None, error_msg











def run_ffmpeg_with_resource_control(cmd, operation_type, timeout=None):
    """
    Запуск FFmpeg с умным контролем ресурсов.
    Добавляет паузы и мониторинг для предотвращения перегрузки.
    """
    if timeout is None:
        timeout = ffmpeg_timeout

    start_time = time.time()

    try:
        # Настраиваем ограничения процесса
        setup_process_limits()

        log_admin(f"Running {operation_type} with resource control: {' '.join(cmd)}", level="info")

        # Добавляем небольшую паузу перед началом для стабилизации системы
        time.sleep(0.5)

        # Запускаем процесс с мониторингом и низким приоритетом
        system = platform.system().lower()
        popen_kwargs = {
            'stdout': subprocess.PIPE,
            'stderr': subprocess.PIPE,
            'text': True
        }

        # Добавляем низкий приоритет для Windows
        if system == 'windows':
            popen_kwargs['creationflags'] = subprocess.BELOW_NORMAL_PRIORITY_CLASS

        process = subprocess.Popen(cmd, **popen_kwargs)

        # Мониторим процесс с периодическими проверками
        check_interval = 2  # Проверяем каждые 2 секунды
        last_check = time.time()

        while process.poll() is None:
            current_time = time.time()

            # Проверяем таймаут
            if current_time - start_time > timeout:
                log_admin(f"FFmpeg timeout ({timeout}s) for {operation_type}", level="warning")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                raise subprocess.TimeoutExpired(cmd, timeout)

            # Периодически проверяем ресурсы и делаем паузы
            if current_time - last_check >= check_interval:
                try:
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                    if cpu_percent > 90:
                        log_admin(f"High CPU detected during FFmpeg: {cpu_percent}%. Adding pause.", level="debug")
                        time.sleep(1)  # Пауза для снижения нагрузки
                except:
                    pass
                last_check = current_time

            time.sleep(0.1)  # Короткая пауза между проверками

        # Получаем результат
        stdout, stderr = process.communicate()

        duration = time.time() - start_time
        log_admin(f"FFmpeg {operation_type} completed in {duration:.2f}s", level="info")

        if process.returncode == 0:
            return True, process, None
        else:
            error_msg = f"FFmpeg failed with return code {process.returncode}: {stderr}"
            log_admin(f"FFmpeg error for {operation_type}: {error_msg}", level="error")
            return False, process, error_msg

    except subprocess.TimeoutExpired:
        log_admin(f"FFmpeg timeout for {operation_type}", level="error")
        return False, None, f"Operation timed out after {timeout} seconds"
    except Exception as e:
        log_admin(f"FFmpeg error for {operation_type}: {e}", level="error")
        return False, None, str(e)


def cleanup_temp_files_if_needed():
    """
    Автоматическая очистка временных файлов при нехватке места на диске.
    Оптимизировано для слабых серверов.
    """
    try:
        import tempfile
        import glob

        temp_dir = tempfile.gettempdir()

        # Проверяем свободное место
        disk = psutil.disk_usage(temp_dir)
        free_mb = disk.free / (1024**2)

        # Если свободного места меньше 200MB, очищаем старые файлы
        if free_mb < 200:
            log_admin(f"Low disk space detected: {free_mb:.2f}MB. Starting cleanup...", level="warning")

            # Удаляем старые файлы подкастов (старше 1 часа)
            current_time = time.time()
            patterns = [
                os.path.join(temp_dir, "podcast_*.mp3"),
                os.path.join(temp_dir, "diana_sasha_podcast_*.mp3"),
                os.path.join(temp_dir, "silence_*.mp3"),
                os.path.join(temp_dir, "*_part1.mp3"),
                os.path.join(temp_dir, "*_part2.mp3"),
                os.path.join(temp_dir, "test_*.mp3")
            ]

            cleaned_count = 0
            cleaned_size = 0

            for pattern in patterns:
                for file_path in glob.glob(pattern):
                    try:
                        # Проверяем возраст файла
                        file_age = current_time - os.path.getmtime(file_path)
                        if file_age > 3600:  # 1 час
                            file_size = os.path.getsize(file_path)
                            os.remove(file_path)
                            cleaned_count += 1
                            cleaned_size += file_size
                            log_admin(f"Cleaned up old file: {file_path} ({file_size} bytes)", level="debug")
                    except Exception as e:
                        log_admin(f"Could not clean file {file_path}: {e}", level="debug")

            if cleaned_count > 0:
                log_admin(f"Cleanup completed: {cleaned_count} files removed, {cleaned_size / (1024**2):.2f}MB freed", level="info")
            else:
                log_admin("No old files found for cleanup", level="info")

    except Exception as e:
        log_admin(f"Error during temp file cleanup: {e}", level="warning")


def monitor_system_health():
    """
    Мониторинг состояния системы для слабых серверов.
    Возвращает рекомендации по оптимизации.
    """
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')

        health_status = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_mb': memory.available / (1024**2),
            'disk_free_mb': disk.free / (1024**2),
            'recommendations': []
        }

        # Анализ и рекомендации
        if cpu_percent > 90:
            health_status['recommendations'].append("High CPU usage - consider using queued processing")

        if memory.percent > 85:
            health_status['recommendations'].append("High memory usage - cleanup recommended")

        if disk.free / (1024**2) < 100:
            health_status['recommendations'].append("Low disk space - immediate cleanup required")
            cleanup_temp_files_if_needed()
        elif disk.free / (1024**2) < 500:
            health_status['recommendations'].append("Disk space getting low - cleanup recommended")

        # Определяем общий статус
        if cpu_percent > 95 or memory.percent > 90 or disk.free / (1024**2) < 50:
            health_status['status'] = 'critical'
        elif cpu_percent > 85 or memory.percent > 80 or disk.free / (1024**2) < 200:
            health_status['status'] = 'warning'
        else:
            health_status['status'] = 'healthy'

        return health_status

    except Exception as e:
        log_admin(f"Error monitoring system health: {e}", level="error")
        return {'status': 'unknown', 'error': str(e)}


def smart_operation_scheduler():
    """
    Умный планировщик операций для распределения нагрузки во времени.
    Определяет оптимальное время для выполнения тяжелых операций.
    """
    try:
        # Проверяем текущую нагрузку
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        # Определяем стратегию выполнения
        if cpu_percent < 50 and memory.percent < 60:
            return {
                'strategy': 'immediate',
                'delay': 0,
                'reason': 'Low system load - execute immediately'
            }
        elif cpu_percent < 80 and memory.percent < 75:
            return {
                'strategy': 'delayed',
                'delay': 2,
                'reason': 'Moderate load - short delay'
            }
        elif cpu_percent < 95 and memory.percent < 85:
            return {
                'strategy': 'queued',
                'delay': 5,
                'reason': 'High load - queue with delay'
            }
        else:
            return {
                'strategy': 'wait',
                'delay': 10,
                'reason': 'Critical load - wait for resources'
            }

    except Exception as e:
        log_admin(f"Error in operation scheduler: {e}", level="warning")
        return {
            'strategy': 'queued',
            'delay': 3,
            'reason': 'Error in scheduler - default to queued'
        }


def execute_with_smart_scheduling(operation_func, *args, **kwargs):
    """
    Выполняет операцию с умным планированием для оптимизации ресурсов.
    """
    schedule = smart_operation_scheduler()

    log_admin(f"Smart scheduler decision: {schedule['strategy']} (delay: {schedule['delay']}s) - {schedule['reason']}", level="info")

    # Применяем задержку если нужно
    if schedule['delay'] > 0:
        log_admin(f"Applying {schedule['delay']}s delay before operation", level="info")
        time.sleep(schedule['delay'])

    # Выполняем операцию
    try:
        return operation_func(*args, **kwargs)
    except Exception as e:
        log_admin(f"Error in scheduled operation: {e}", level="error")
        raise













def _fallback_simple_concat(audio_file_paths, output_path):
    """
    Fallback method for combining audio files using simple concatenation.
    """
    log_admin(f"=== FALLBACK CONCATENATION START ===", level="warning")
    log_admin(f"Processing {len(audio_file_paths)} files with fallback method", level="info")

    try:
        import tempfile
        import uuid

        temp_dir = tempfile.gettempdir()
        concat_filename = f"podcast_concat_{uuid.uuid4().hex[:8]}.txt"
        concat_file_path = os.path.join(temp_dir, concat_filename)

        log_admin(f"Creating concat file: {concat_file_path}", level="info")

        # Write the concat file
        with open(concat_file_path, 'w', encoding='utf-8') as f:
            for i, audio_path in enumerate(audio_file_paths):
                # Escape single quotes in file paths for FFmpeg
                escaped_path = audio_path.replace("'", "'\"'\"'")
                f.write(f"file '{escaped_path}'\n")
                log_admin(f"Added to concat file {i+1}: {audio_path}", level="info")

        log_admin(f"Created fallback concat file: {concat_file_path}", level="info")

        # Get server-optimized parameters for fallback (качество сохранено)
        try:
            from server_config import get_ffmpeg_params
            params = get_ffmpeg_params()
        except ImportError:
            params = {
                'threads': 1,
                'preset': 'fast',
                'audio_bitrate': '128k',
                'sample_rate': 44100,
                'channels': 1
            }

        # Run FFmpeg to combine audio files - качество + контроль ресурсов
        base_ffmpeg_cmd = [
            'ffmpeg', '-y',
            '-f', 'concat',
            '-safe', '0',
            '-i', concat_file_path,
            '-af', 'loudnorm=I=-16:TP=-1.5:LRA=11',  # Качественная нормализация
            '-ar', str(params['sample_rate']),
            '-ac', str(params['channels']),
            '-acodec', 'libmp3lame',
            '-b:a', params['audio_bitrate'],
            '-threads', str(params['threads']),
            '-preset', params['preset'],
            output_path
        ]
        ffmpeg_cmd = create_ffmpeg_command_with_priority(base_ffmpeg_cmd)

        log_admin(f"Running fallback command: {' '.join(ffmpeg_cmd)}", level="info")
        log_ffmpeg_operation_start("Fallback audio concatenation", audio_file_paths, output_path)
        success, result, error_msg = run_ffmpeg_with_resource_control(
            ffmpeg_cmd, "Fallback audio concatenation"
        )

        # Clean up concat file
        try:
            os.remove(concat_file_path)
            log_admin(f"Cleaned up concat file: {concat_file_path}", level="info")
        except Exception as e:
            log_admin(f"Error removing concat file {concat_file_path}: {e}", level="warning")

        if not success:
            log_admin(f"FAILED: Fallback FFmpeg failed: {error_msg}", level="error")
            return None

        # Verify output file was created
        if os.path.exists(output_path):
            output_size = os.path.getsize(output_path)
            log_admin(f"SUCCESS: Combined {len(audio_file_paths)} audio files (fallback). Output: {output_path} (size: {output_size} bytes)", level="info")
            return output_path
        else:
            log_admin(f"ERROR: Fallback output file was not created: {output_path}", level="error")
            return None

    except Exception as e:
        log_admin(f"CRITICAL ERROR in fallback concat: {e}", level="error")
        log_admin(f"Exception traceback: {traceback.format_exc()}", level="error")
        return None


def format_research_podcast_system_prompt(base_prompt, is_private_chat, requester_info="", user_preferences=""):
    """
    Formats research podcast system prompt with appropriate constraints based on chat type.

    Since the prompts are now used only for private chats (with 9000 chars + [PODCAST_BREAK]),
    this function now just formats the prompt with requester info and user preferences.
    For group chats, different prompts are used entirely.

    Args:
        base_prompt: Base system prompt template (already contains 9000 char constraints)
        is_private_chat: True if private chat, False if group chat (not used anymore)
        requester_info: Information about who requested the podcast
        user_preferences: User preferences for the podcast

    Returns:
        Formatted system prompt string
    """
    # Simply format the prompt with the provided parameters
    # The base_prompt already contains the correct constraints for private chats
    formatted_prompt = base_prompt.format(
        requester_info=requester_info,
        user_preferences=user_preferences
    )

    return formatted_prompt



def format_podcast_system_prompt(base_prompt, is_private_chat, current_datetime, theme_instructions="", duration_minutes=10):
    """
    Formats regular podcast system prompt with appropriate constraints and personality based on chat type.

    Args:
        base_prompt: Base system prompt template with placeholders
        is_private_chat: True if private chat, False if group chat
        current_datetime: Current date and time string
        theme_instructions: Optional theme instructions for themed podcasts
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10

    Returns:
        Formatted system prompt string
    """
    from config import (PODCAST_BATCH_INSTRUCTIONS, DIANA_PERSONALITY_GROUP, DIANA_PERSONALITY_PRIVATE,
                       format_podcast_length_constraints)

    # Choose Diana's personality and constraints based on chat type
    if is_private_chat:
        diana_personality = DIANA_PERSONALITY_PRIVATE
        # For private chats, add BATCH instructions
        chat_type_instructions = PODCAST_BATCH_INSTRUCTIONS
    else:
        diana_personality = DIANA_PERSONALITY_GROUP
        # For group chats, no BATCH instructions (single part only)
        chat_type_instructions = ""

    # Get dynamic length constraints based on duration
    length_constraints = format_podcast_length_constraints(duration_minutes, is_private_chat)

    # Format the prompt with all parameters
    formatted_prompt = base_prompt.format(
        current_datetime=current_datetime,
        theme_instructions=theme_instructions,
        diana_personality=diana_personality,
        chat_type_instructions=chat_type_instructions,
        length_constraints=length_constraints
    )

    return formatted_prompt


def format_thematic_podcast_system_prompt(base_prompt, user_request, user_nickname, current_datetime, research_info="", duration_minutes=10):
    """
    Formats thematic podcast system prompt with user request, nickname, and research information.

    Args:
        base_prompt: Base system prompt template with placeholders
        user_request: User's thematic request (the topic)
        user_nickname: User's nickname
        current_datetime: Current date and time string
        research_info: Optional research information from internet search
        duration_minutes: Duration of podcast in minutes (3, 5, or 10), defaults to 10

    Returns:
        Formatted system prompt string
    """
    from config import DIANA_PERSONALITY_PRIVATE, format_podcast_length_constraints  # Always use private personality for thematic podcasts

    # Get dynamic length constraints based on duration (thematic podcasts always use private chat constraints)
    length_constraints = format_podcast_length_constraints(duration_minutes, is_private_chat=True)

    # Format the prompt with all parameters
    formatted_prompt = base_prompt.format(
        user_request=user_request,
        user_nickname=user_nickname,
        current_datetime=current_datetime,
        diana_personality=DIANA_PERSONALITY_PRIVATE,
        research_info=research_info,
        length_constraints=length_constraints
    )

    return formatted_prompt


def save_tts_audio(audio_data, chat_id, timestamp, process_key=None):
    """
    Saves TTS audio data (raw PCM from Gemini TTS) to a temporary file and converts to MP3.

    Args:
        audio_data: Audio data as bytes (raw PCM format from Gemini TTS)
        chat_id: Chat ID for filename
        timestamp: Timestamp for filename
        process_key: Unique process identifier for filename uniqueness

    Returns:
        Path to saved MP3 file or None on error
    """
    if not audio_data:
        return None

    try:
        import tempfile
        import subprocess

        # Save raw PCM audio first (Gemini TTS returns raw PCM, not WAV)
        if process_key:
            raw_filename = f"tts_raw_{process_key}_{timestamp}.pcm"
            mp3_filename = f"tts_{process_key}_{timestamp}.mp3"
        else:
            raw_filename = f"tts_raw_{chat_id}_{timestamp}.pcm"
            mp3_filename = f"tts_{chat_id}_{timestamp}.mp3"

        raw_filepath = os.path.join(tempfile.gettempdir(), raw_filename)

        with open(raw_filepath, "wb") as f:
            f.write(audio_data)

        log_admin(f"TTS raw PCM audio saved to: {raw_filepath}")

        # Convert raw PCM to MP3 using ffmpeg
        mp3_filepath = os.path.join(tempfile.gettempdir(), mp3_filename)

        try:
            # Gemini TTS returns 16-bit signed little-endian PCM at 24kHz mono
            ffmpeg_cmd = [
                "ffmpeg", "-y",
                "-f", "s16le",       # Input format: 16-bit signed little-endian PCM
                "-ar", "24000",      # Input sample rate: 24kHz
                "-ac", "1",          # Input channels: 1 (mono)
                "-i", raw_filepath,  # Input raw PCM file
                "-acodec", "libmp3lame",
                "-b:a", "128k",
                "-ar", "44100",      # Output sample rate: 44.1kHz (standard)
                "-ac", "1",          # Output channels: 1 (mono)
                mp3_filepath
            ]

            log_admin(f"Converting TTS raw PCM to MP3: {' '.join(ffmpeg_cmd)}")

            # Use the safer subprocess function with better error handling
            success, returncode, stdout, stderr = safe_subprocess_run(
                ffmpeg_cmd, timeout=60, operation_name="TTS raw PCM to MP3 conversion"
            )

            if success and returncode == 0:
                # Clean up raw PCM file
                try:
                    os.remove(raw_filepath)
                except Exception as cleanup_error:
                    log_admin(f"Warning: Could not remove raw PCM file {raw_filepath}: {cleanup_error}", level="warning")

                log_admin(f"TTS audio successfully converted to MP3: {mp3_filepath}")
                return mp3_filepath
            else:
                log_admin(f"❌ FFmpeg conversion failed with return code {returncode}. STDOUT: {stdout[:200]} STDERR: {stderr[:500]}", level="error")
                # Clean up raw file on failure
                try:
                    if os.path.exists(raw_filepath):
                        os.remove(raw_filepath)
                except Exception:
                    pass
                return None  # Return None instead of broken file

        except Exception as convert_error:
            log_admin(f"Error converting TTS audio with ffmpeg: {convert_error}")
            import traceback
            log_admin(f"Traceback: {traceback.format_exc()}")
            # Clean up raw file on exception
            try:
                if os.path.exists(raw_filepath):
                    os.remove(raw_filepath)
            except Exception:
                pass
            return None  # Return None instead of broken file

    except Exception as e:
        log_admin(f"Error saving TTS audio: {e}")
        return None


def normalize_audio_file(input_path, output_path):
    """
    Нормализует аудиофайл для предотвращения проблем со скоростью воспроизведения.

    Args:
        input_path: Путь к входному файлу
        output_path: Путь к выходному файлу

    Returns:
        True если успешно, False если ошибка
    """
    try:
        # Команда для нормализации аудио с принудительным sample rate
        # Используем простую пересэмплинг без soxr для совместимости
        command = [
            'ffmpeg', '-y',
            '-i', input_path,
            '-ar', '44100',  # Принудительно 44.1kHz
            '-ac', '1',      # Моно
            '-acodec', 'libmp3lame',
            '-b:a', '128k',
            '-af', 'aresample=44100',  # Простая пересэмплинг
            output_path
        ]

        log_admin(f"Normalizing audio: {' '.join(command)}")

        result = subprocess.run(command, check=True, capture_output=True, text=True, timeout=120)
        log_admin(f"Audio normalization successful: {output_path}")
        return True

    except Exception as e:
        log_admin(f"Audio normalization failed: {e}")
        return False


def save_podcast_audio(audio_data, chat_id, timestamp, suffix="", process_key=None):
    """
    Saves podcast audio data to a temporary file and converts to MP3 if needed.

    Args:
        audio_data: Audio data as bytes
        chat_id: Chat ID for filename
        timestamp: Timestamp for filename
        suffix: Optional suffix for filename (e.g., "_part1", "_part2")
        process_key: Unique process identifier for filename uniqueness

    Returns:
        Path to saved file or None on error
    """
    if not audio_data:
        return None

    try:
        import tempfile

        # Save raw audio first - use process_key for uniqueness if available
        if process_key:
            raw_filename = f"podcast_raw_{process_key}_{timestamp}{suffix}.bin"
            mp3_filename = f"podcast_{process_key}_{timestamp}{suffix}.mp3"
        else:
            raw_filename = f"podcast_raw_{chat_id}_{timestamp}{suffix}.bin"
            mp3_filename = f"podcast_{chat_id}_{timestamp}{suffix}.mp3"

        raw_filepath = os.path.join(tempfile.gettempdir(), raw_filename)

        with open(raw_filepath, "wb") as f:
            f.write(audio_data)

        log_admin(f"Raw podcast audio saved to: {raw_filepath}")

        # Convert to MP3 using ffmpeg
        mp3_filepath = os.path.join(tempfile.gettempdir(), mp3_filename)

        try:
            # Gemini TTS возвращает аудио в сыром формате
            # Попробуем разные подходы для конвертации с улучшенными параметрами
            import subprocess
            import sys

            # Попробуем несколько форматов входного файла с правильными параметрами
            formats_to_try = [
                # Gemini TTS обычно возвращает 24kHz PCM
                ["-f", "s16le", "-ar", "24000", "-ac", "1"],  # 16-bit PCM, 24kHz, mono (наиболее вероятный формат Gemini)
                ["-f", "s16le", "-ar", "22050", "-ac", "1"],  # 16-bit PCM, 22kHz, mono
                ["-f", "s16le", "-ar", "44100", "-ac", "1"],  # 16-bit PCM, 44kHz, mono
                ["-f", "s16le", "-ar", "16000", "-ac", "1"],  # 16-bit PCM, 16kHz, mono
                # Попробуем как WAV без заголовка
                ["-f", "wav"],
                # Попробуем автодетект
                []
            ]

            for i, input_format in enumerate(formats_to_try):
                try:
                    ffmpeg_cmd = ["ffmpeg", "-y"] + input_format + [
                        "-i", raw_filepath,
                        "-acodec", "libmp3lame",
                        "-ab", "128k",
                        "-ar", "44100",  # Стандартизируем выходную частоту
                        "-ac", "1",      # Принудительно моно
                        "-af", "aresample=44100",  # Принудительная пересэмплинг для предотвращения проблем со скоростью
                        mp3_filepath
                    ]

                    log_admin(f"Trying conversion method {i+1}: {' '.join(ffmpeg_cmd)}")

                    # Use safer subprocess execution with proper signal handling
                    process = None
                    try:
                        # Check system resources before starting FFmpeg
                        try:
                            import psutil
                            memory = psutil.virtual_memory()
                            if memory.percent > 90:
                                log_admin(f"High memory usage ({memory.percent}%) detected before FFmpeg method {i+1}", level="warning")
                        except Exception:
                            pass

                        process = subprocess.Popen(
                            ffmpeg_cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE,
                            text=True,
                            preexec_fn=None if sys.platform.startswith('win') else os.setsid
                        )

                        # Wait with timeout and monitor process (increased timeout for large audio files)
                        stdout, stderr = process.communicate(timeout=1200)

                        if process.returncode == 0:
                            # Clean up raw file safely
                            try:
                                if os.path.exists(raw_filepath):
                                    os.remove(raw_filepath)
                            except Exception as cleanup_error:
                                log_admin(f"Warning: Could not remove raw file {raw_filepath}: {cleanup_error}", level="warning")

                            log_admin(f"Podcast audio successfully converted to MP3 (method {i+1}): {mp3_filepath}")
                            return mp3_filepath
                        else:
                            log_admin(f"Method {i+1} failed with return code {process.returncode}: {stderr[:200]}")

                    except subprocess.TimeoutExpired:
                        if process:
                            try:
                                # Terminate process gracefully
                                import signal
                                if sys.platform.startswith('win'):
                                    process.terminate()
                                else:
                                    os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                                process.wait(timeout=5)
                            except Exception:
                                try:
                                    if sys.platform.startswith('win'):
                                        process.kill()
                                    else:
                                        os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                                except Exception:
                                    pass
                        log_admin(f"Method {i+1} timed out after 1200 seconds", level="warning")

                except Exception as method_error:
                    log_admin(f"Method {i+1} exception: {method_error}", level="warning")
                    # Force cleanup of any hanging processes
                    try:
                        import psutil
                        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                            try:
                                if proc.info['name'] == 'ffmpeg' and any('podcast_raw_' in str(arg) for arg in proc.info['cmdline'] or []):
                                    log_admin(f"Terminating hanging FFmpeg process PID {proc.info['pid']}", level="warning")
                                    proc.terminate()
                            except Exception:
                                pass
                    except Exception:
                        pass
                    continue

            # Если все методы не сработали, попробуем сохранить как WAV
            log_admin("All FFmpeg methods failed, trying WAV approach...")
            raise Exception("All conversion methods failed")

        except Exception as convert_error:
            log_admin(f"Error converting audio with ffmpeg: {convert_error}")

            # Fallback: попробуем добавить WAV заголовок и сохранить как WAV
            try:
                if process_key:
                    wav_filename = f"podcast_{process_key}_{timestamp}{suffix}.wav"
                else:
                    wav_filename = f"podcast_{chat_id}_{timestamp}{suffix}.wav"
                wav_filepath = os.path.join(tempfile.gettempdir(), wav_filename)

                # Попробуем создать простой WAV файл с заголовком
                with open(raw_filepath, "rb") as raw_file:
                    raw_data = raw_file.read()

                # Создаем простой WAV заголовок (44 байта)
                # Предполагаем: 24000 Hz, 16-bit, mono (наиболее вероятный формат Gemini TTS)
                sample_rate = 24000
                bits_per_sample = 16
                channels = 1
                byte_rate = sample_rate * channels * bits_per_sample // 8
                block_align = channels * bits_per_sample // 8
                data_size = len(raw_data)
                file_size = 36 + data_size

                wav_header = (
                    b'RIFF' +
                    file_size.to_bytes(4, 'little') +
                    b'WAVE' +
                    b'fmt ' +
                    (16).to_bytes(4, 'little') +  # fmt chunk size
                    (1).to_bytes(2, 'little') +   # audio format (PCM)
                    channels.to_bytes(2, 'little') +
                    sample_rate.to_bytes(4, 'little') +
                    byte_rate.to_bytes(4, 'little') +
                    block_align.to_bytes(2, 'little') +
                    bits_per_sample.to_bytes(2, 'little') +
                    b'data' +
                    data_size.to_bytes(4, 'little')
                )

                # Записываем WAV файл
                with open(wav_filepath, "wb") as wav_file:
                    wav_file.write(wav_header + raw_data)

                log_admin(f"Created WAV file with header: {wav_filepath}")

                # Теперь конвертируем WAV в MP3 с правильными параметрами
                ffmpeg_cmd = [
                    "ffmpeg", "-y",
                    "-i", wav_filepath,
                    "-acodec", "libmp3lame",
                    "-ab", "128k",
                    "-ar", "44100",
                    "-ac", "1",  # Принудительно моно
                    "-af", "aresample=44100",  # Принудительная пересэмплинг
                    mp3_filepath
                ]

                success, returncode, stdout, stderr = safe_subprocess_run(
                    ffmpeg_cmd, timeout=60, operation_name="WAV to MP3 conversion"
                )

                if success:
                    # Clean up temp files safely
                    try:
                        if os.path.exists(raw_filepath):
                            os.remove(raw_filepath)
                        if os.path.exists(wav_filepath):
                            os.remove(wav_filepath)
                    except Exception as cleanup_error:
                        log_admin(f"Warning: Could not clean up temp files: {cleanup_error}", level="warning")

                    log_admin(f"Successfully converted WAV to MP3: {mp3_filepath}")
                    return mp3_filepath
                else:
                    log_admin(f"WAV to MP3 conversion failed: {stderr}")
                    # Оставляем WAV файл как есть, переименовываем в MP3
                    try:
                        if os.path.exists(raw_filepath):
                            os.remove(raw_filepath)
                        if os.path.exists(wav_filepath):
                            os.rename(wav_filepath, mp3_filepath)
                            log_admin(f"Saved WAV as MP3: {mp3_filepath}")
                            return mp3_filepath
                    except Exception as rename_error:
                        log_admin(f"Error renaming WAV to MP3: {rename_error}", level="warning")

            except Exception as wav_error:
                log_admin(f"WAV creation also failed: {wav_error}")
                # Последний шанс: просто переименовываем сырой файл
                try:
                    os.rename(raw_filepath, mp3_filepath)
                    log_admin(f"Final fallback - renamed raw file to MP3: {mp3_filepath}")
                    return mp3_filepath
                except Exception as final_error:
                    log_admin(f"Even renaming failed: {final_error}")
                    return None

    except Exception as e:
        log_admin(f"Error saving podcast audio: {e}", level="error")
        log_admin(f"Exception traceback: {traceback.format_exc()}", level="error")

        # Final cleanup attempt
        try:
            cleanup_hanging_ffmpeg_processes(chat_id)
        except Exception as cleanup_error:
            log_admin(f"Error in final cleanup: {cleanup_error}", level="warning")

        # Try to clean up any leftover files
        try:
            if raw_filepath and os.path.exists(raw_filepath):
                os.remove(raw_filepath)
                log_admin(f"Cleaned up raw file: {raw_filepath}", level="debug")
        except Exception:
            pass

        try:
            if mp3_filepath and os.path.exists(mp3_filepath):
                # Check if file is corrupted/empty and remove it
                if os.path.getsize(mp3_filepath) < 1024:  # Less than 1KB
                    os.remove(mp3_filepath)
                    log_admin(f"Removed corrupted MP3 file: {mp3_filepath}", level="debug")
        except Exception:
            pass

        return None























def safe_delete_message(bot, chat_id, message_id, max_retries=3, delay=1):
    """
    Safely deletes a message with retry logic and proper error handling.
    
    Args:
        bot: Bot instance
        chat_id: Chat ID
        message_id: Message ID to delete
        max_retries: Maximum number of retry attempts
        delay: Delay between retries in seconds
    
    Returns:
        bool: True if message was deleted successfully, False otherwise
    """
    if not message_id:
        return False
        
    for attempt in range(max_retries):
        try:
            bot.delete_message(chat_id, message_id)
            log_admin(f"Successfully deleted message {message_id} in chat {chat_id}")
            return True
        except Exception as e:
            error_str = str(e).lower()
            
            # If message is already deleted or not found, consider it successful
            if "message to delete not found" in error_str or "message can't be deleted" in error_str:
                log_admin(f"Message {message_id} in chat {chat_id} already deleted or not found")
                return True
            
            # If this is the last attempt, log the error
            if attempt == max_retries - 1:
                log_admin(f"Failed to delete message {message_id} in chat {chat_id} after {max_retries} attempts: {e}", level="error")
                return False
            else:
                log_admin(f"Attempt {attempt + 1} to delete message {message_id} in chat {chat_id} failed: {e}, retrying...", level="warning")
                time.sleep(delay)
    
    return False


def generate_podcast_title(chat_messages, chat_id):
    """
    Generates a title for the podcast based on chat content using AI.

    Args:
        chat_messages: List of chat messages
        chat_id: Chat ID

    Returns:
        Generated title string
    """
    if not chat_messages:
        return "Подкаст чата"

    try:
        # Extract text content from messages
        all_text = " ".join([msg.get('text', '') for msg in chat_messages if msg.get('text')])

        # Limit text length for API call
        if len(all_text) > 2000:
            all_text = all_text[:2000] + "..."

        if not all_text.strip():
            # Fallback if no text content
            from datetime import datetime
            current_date = datetime.now().strftime('%d.%m.%Y')
            return f"Подкаст чата • {current_date}"

        # Generate title using AI
        from api_clients import call_gemini_2_5_flash_api

        system_prompt = """Ты создаешь привлекательные названия для подкастов на основе содержания чата.

ТРЕБОВАНИЯ:
• Название должно быть коротким (максимум 50 символов)
• Отражать основные темы обсуждения
• Быть интересным и привлекательным
• На русском языке
• БЕЗ эмодзи и специальных символов
• Формат: просто название без слова "Подкаст:" в начале

ПРИМЕРЫ ХОРОШИХ НАЗВАНИЙ:
• "Технологии и будущее"
• "Обсуждение фильмов"
• "Планы на выходные"
• "Работа и карьера"
• "Игры и развлечения"

Создай ОДНО короткое название на основе содержания чата."""

        user_prompt = f"Создай название подкаста на основе этих сообщений из чата:\n\n{all_text}"

        title = call_gemini_2_5_flash_api(
            history=[],
            user_text=user_prompt,
            input_data=None,
            system_prompt=system_prompt,
            call_type="podcast_title"
        )

        if title and not title.startswith("ОШИБКА") and not title.startswith("Извините"):
            # Clean up the title
            title = title.strip().replace('\n', ' ').replace('\r', '')

            # Remove quotes if present
            if title.startswith('"') and title.endswith('"'):
                title = title[1:-1]
            if title.startswith("'") and title.endswith("'"):
                title = title[1:-1]

            # Limit length
            if len(title) > 50:
                title = title[:47] + "..."

            log_admin(f"Generated AI podcast title: '{title}'")
            return title
        else:
            log_admin(f"AI title generation failed: {title}")
            raise Exception("AI generation failed")

    except Exception as e:
        log_admin(f"Error generating AI podcast title: {e}")

        # Fallback to date-based title
        from datetime import datetime
        now = datetime.now()
        current_date = now.strftime('%d.%m.%Y')
        current_time = now.strftime('%H:%M')

        # Try to extract some keywords as backup
        try:
            all_text = " ".join([msg.get('text', '') for msg in chat_messages if msg.get('text')])
            common_words = ['и', 'в', 'на', 'с', 'по', 'для', 'от', 'до', 'из', 'к', 'о', 'об', 'что', 'как', 'это', 'не', 'да', 'нет', 'а', 'но', 'или', 'то', 'же', 'бы', 'за', 'при', 'со', 'во', 'ко', 'ли', 'ни', 'уже', 'еще', 'так', 'там', 'тут', 'где', 'когда', 'если', 'чтобы']
            words = all_text.lower().split()
            interesting_words = [w for w in words if len(w) > 4 and w not in common_words and w.isalpha()]

            if interesting_words:
                # Take first interesting word as topic hint
                topic_hint = interesting_words[0].capitalize()
                return f"Обсуждение: {topic_hint}"
            else:
                return f"Подкаст чата • {current_date}"
        except:
            return f"Подкаст чата • {current_date}"


def safe_caption_for_telegram(caption_text, max_length=900):
    """
    Safely truncates caption text to avoid Telegram API limits.

    Args:
        caption_text: Original caption text
        max_length: Maximum allowed length (default 1000, Telegram limit is 1024 for regular users)

    Returns:
        Truncated caption text that fits within Telegram limits
    """
    if not caption_text:
        return ""

    # Convert to string if not already
    caption_text = str(caption_text)

    # If within limit, return as is
    if len(caption_text) <= max_length:
        return caption_text

    # Truncate and add ellipsis
    truncated = caption_text[:max_length-3] + "..."

    from bot_globals import log_admin
    log_admin(f"Caption truncated from {len(caption_text)} to {len(truncated)} characters to avoid Telegram limits")

    return truncated


def generate_research_podcast_title(topic):
    """
    Generates an improved title for research podcast using Gemini 2.5 Flash.

    Args:
        topic: Original research topic/query from user

    Returns:
        Improved title string with better formatting and grammar
    """
    if not topic or not topic.strip():
        return "Исследование"

    try:
        # Generate improved title using Gemini 2.5 Flash
        from api_clients import call_gemini_2_5_flash_api

        system_prompt = """Ты улучшаешь названия для подкастов исследований, исправляя грамматику и форматирование.

ТРЕБОВАНИЯ:
• Исправь грамматические ошибки в запросе пользователя
• Улучши форматирование и стиль
• Сохрани исходный смысл и тему
• Сделай название привлекательным и читаемым
• Максимум 60 символов
• На русском языке
• БЕЗ эмодзи и специальных символов
• НЕ добавляй слово "Исследование:" в начало - это будет добавлено автоматически

ПРИМЕРЫ:
Исходный запрос: "как работает нейросети"
Улучшенное название: "Как работают нейросети"

Исходный запрос: "что такое квантовые компьютеры и зачем они нужны"
Улучшенное название: "Квантовые компьютеры и их применение"

Исходный запрос: "история развития интернета"
Улучшенное название: "История развития интернета"

Верни ТОЛЬКО улучшенное название без дополнительных слов."""

        user_prompt = f"Улучши это название для подкаста исследования:\n\n{topic}"

        improved_title = call_gemini_2_5_flash_api(
            history=[],
            user_text=user_prompt,
            input_data=None,
            system_prompt=system_prompt,
            call_type="research_podcast_title"
        )

        if improved_title and not improved_title.startswith("ОШИБКА") and not improved_title.startswith("Извините"):
            # Clean up the title
            improved_title = improved_title.strip().replace('\n', ' ').replace('\r', '')

            # Remove quotes if present
            if improved_title.startswith('"') and improved_title.endswith('"'):
                improved_title = improved_title[1:-1]
            if improved_title.startswith("'") and improved_title.endswith("'"):
                improved_title = improved_title[1:-1]

            # Limit length and add prefix
            if len(improved_title) > 60:
                improved_title = improved_title[:57] + "..."

            final_title = f"Исследование: {improved_title}"
            log_admin(f"Generated improved research podcast title: '{final_title}'")
            return final_title
        else:
            log_admin(f"GPT-4.1 title improvement failed: {improved_title}")
            raise Exception("GPT-4.1 generation failed")

    except Exception as e:
        log_admin(f"Error generating improved research podcast title: {e}")

        # Fallback to original logic with some basic improvements
        try:
            # Basic cleanup of the original topic
            cleaned_topic = topic.strip()

            # Capitalize first letter if needed
            if cleaned_topic and cleaned_topic[0].islower():
                cleaned_topic = cleaned_topic[0].upper() + cleaned_topic[1:]

            # Limit length
            if len(cleaned_topic) > 50:
                cleaned_topic = cleaned_topic[:47] + "..."

            return f"Исследование: {cleaned_topic}"
        except:
            return "Исследование"

def extract_code_block(text):
    if not isinstance(text, str):
        return None
    patterns = [
        r"```(?:python|py)\s*\n?(.*?)```",
        r"```\s*\n?(.*?)```"
    ]
    for pattern in patterns:
        match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
        if match:
            code = match.group(1).strip()
            log_admin(f"Extracted code block using pattern: {pattern[:20]}... length: {len(code)}", level="debug")
            return code

    return None

def get_target_message_content(message):
    user_id = message.from_user.id
    target_content = None
    if message.reply_to_message and \
       message.reply_to_message.from_user and \
       message.reply_to_message.from_user.is_bot and \
       message.reply_to_message.from_user.id == bot.get_me().id:
        replied_msg = message.reply_to_message
        message_key = f"{replied_msg.chat.id}_{replied_msg.message_id}"
        with message_states_lock:
            state = message_states.get(message_key)
            if state and state.get('type') == 'text' and state.get('full_original_text'):
                target_content = state['full_original_text']
                log_admin(f"User {user_id} - fetching full original content from message state for replied bot message {replied_msg.message_id}.", level="debug")
        if target_content is None:
            if replied_msg.text: target_content = replied_msg.text
            elif replied_msg.caption: target_content = replied_msg.caption
            if target_content: log_admin(f"User {user_id} - fetching visible content from replied bot message {replied_msg.message_id}.", level="debug")
    if target_content is None:
        with user_last_response_lock:
            target_content = user_last_response.get(user_id)
        if target_content: log_admin(f"User {user_id} - fetching content from last response cache.", level="debug")
        else: log_admin(f"User {user_id} - no replied message or cached response found.", level="debug")
    return target_content

def parse_gemini_audio_summary_output(text: str, num_expected_items: int) -> list:
    if not isinstance(text, str):
        log_admin("Parser: Input text is not a string.", level="warning")
        return [{"short_summary": "[Ошибка: входные данные не являются строкой]",
                 "detailed_summary": "[Ошибка: входные данные не являются строкой]",
                 "formatted_transcript": "[Ошибка: входные данные не являются строкой]"}] * num_expected_items if num_expected_items > 0 else []
    text = text.strip()
    log_admin(f"Parser input text (len: {len(text)}, first 500char): '{text[:500]}'", level="debug")

    # ДЕБАГ: Детальное логирование входных данных парсера
    log_admin(
        f"Parser: DEBUG_PARSER_INPUT - Full input analysis: "
        f"Text length: {len(text)}, "
        f"Expected items: {num_expected_items}, "
        f"Text contains SHORT_SUMMARY: {'SHORT_SUMMARY' in text}, "
        f"Text contains DETAILED_SUMMARY: {'DETAILED_SUMMARY' in text}, "
        f"Text contains FORMATTED_TRANSCRIPT: {'FORMATTED_TRANSCRIPT' in text}, "
        f"Full text: '{text}'",
        level="debug"
    )
    final_list = []
    error_placeholder_short = "[Краткая сводка не найдена]"
    error_placeholder_detailed = "[Подробная сводка не найдена]"
    error_placeholder_transcript = "[Расшифровка не найдена]"
    parser_critical_error_msg = "[Ошибка: не удалось разобрать ответ модели]"
    for i in range(1, num_expected_items + 1):
        item_result = {
            'short_summary': error_placeholder_short,
            'detailed_summary': error_placeholder_detailed,
            'formatted_transcript': error_placeholder_transcript
        }
        found_any_tag_for_item = False
        short_summary_match = re.search(rf"<<SHORT_SUMMARY_{i}_START>>\s*(.*?)\s*<<SHORT_SUMMARY_{i}_END>>", text, re.DOTALL | re.IGNORECASE)
        if short_summary_match:
            content = html.unescape(short_summary_match.group(1).strip())
            item_result['short_summary'] = content if content else error_placeholder_short
            found_any_tag_for_item = True
            log_admin(f"Parser: Found SHORT_SUMMARY_{i}: '{content[:100]}...'", level="debug")
        else:
            log_admin(f"Parser: SHORT_SUMMARY_{i} not found.", level="debug")
        detailed_summary_match = re.search(rf"<<DETAILED_SUMMARY_{i}_START>>\s*(.*?)\s*<<DETAILED_SUMMARY_{i}_END>>", text, re.DOTALL | re.IGNORECASE)
        if detailed_summary_match:
            content = html.unescape(detailed_summary_match.group(1).strip())
            item_result['detailed_summary'] = content if content else error_placeholder_detailed
            found_any_tag_for_item = True
            log_admin(f"Parser: Found DETAILED_SUMMARY_{i}: '{content[:100]}...'", level="debug")
        else:
            log_admin(f"Parser: DETAILED_SUMMARY_{i} not found.", level="debug")
        formatted_transcript_match = re.search(rf"<<FORMATTED_TRANSCRIPT_{i}_START>>\s*(.*?)\s*<<FORMATTED_TRANSCRIPT_{i}_END>>", text, re.DOTALL | re.IGNORECASE)
        if formatted_transcript_match:
            content = html.unescape(formatted_transcript_match.group(1).strip())
            item_result['formatted_transcript'] = content if content else error_placeholder_transcript
            found_any_tag_for_item = True
            log_admin(f"Parser: Found FORMATTED_TRANSCRIPT_{i}: '{content[:100]}...'", level="debug")
        else:
            log_admin(f"Parser: FORMATTED_TRANSCRIPT_{i} not found.", level="debug")
        final_list.append(item_result)
        if not found_any_tag_for_item and text.strip():
            log_admin(f"Parser: For item index {i}, no specific tags were found. Raw text might be malformed, or model did not provide tags for this item.", level="warning")
    if not final_list and num_expected_items > 0:
        log_admin(f"Parser: Final list is unexpectedly empty after trying to parse {num_expected_items} items. Input was: '{text[:200]}...'", level="error")
        return [{"short_summary": parser_critical_error_msg,
                 "detailed_summary": parser_critical_error_msg,
                 "formatted_transcript": parser_critical_error_msg}] * num_expected_items
    all_items_are_critical_error = True
    if final_list:
        for item in final_list:
            is_item_fully_placeholder = (item['short_summary'] == error_placeholder_short and
                                        item['detailed_summary'] == error_placeholder_detailed and
                                        item['formatted_transcript'] == error_placeholder_transcript)
            if not is_item_fully_placeholder:
                all_items_are_critical_error = False
                break
    elif num_expected_items > 0:
         all_items_are_critical_error = True
    if all_items_are_critical_error and text.strip() and num_expected_items > 0:
        log_admin(f"Parser: All expected items ({num_expected_items}) resulted in default 'not found' values or list was empty, "
                  f"despite non-empty input text. This suggests a complete failure to match any tags. "
                  f"Input text (first 200): '{text[:200]}'", level="warning")

        # ДЕБАГ: Детальное логирование ошибки парсера
        log_admin(
            f"Parser: DEBUG_PARSER_CRITICAL_ERROR - Complete parsing failure: "
            f"Expected {num_expected_items} items, "
            f"Input text length: {len(text)}, "
            f"Full input text: '{text}', "
            f"Looking for tags: SHORT_SUMMARY_1_START/END, DETAILED_SUMMARY_1_START/END, FORMATTED_TRANSCRIPT_1_START/END",
            level="error"
        )

        final_list = [{"short_summary": parser_critical_error_msg,
                       "detailed_summary": parser_critical_error_msg,
                       "formatted_transcript": parser_critical_error_msg}] * num_expected_items
    log_admin(f"Parser: Final parsed list (first item if exists): {str(final_list[0]) if final_list else '[]'}", level="info")
    return final_list

# --- Group Message Processing Function ---
def process_text_for_group_chat(text_content, chat_id, user_id):
    """
    ОТКЛЮЧЕНО: Функция больше не обрабатывает текст для групп.
    Возвращает текст без изменений для всех чатов.

    Ранее обрабатывала текст перед отправкой в группы:
    - Сокращала до 150 символов если больше 200
    - Преобразовывала в строчные буквы
    - Удаляла звездочки
    """
    # ЭТАП 3: Убираем автосокращение в группах - возвращаем текст без изменений
    log_admin(f"process_text_for_group_chat: returning text unchanged (length: {len(text_content)})")
    return text_content


def send_multiple_messages_with_break(bot_instance, chat_id, text_content, reply_to_message_id, user_id=None):
    """
    Обрабатывает сообщения с тегом [BREAK] для отправки множественных сообщений.
    Первое сообщение отправляется в реплай, остальные - отдельно с задержкой.
    Устанавливает блокировку чата чтобы не обрабатывать новые сообщения во время отправки.
    
    Args:
        bot_instance: Экземпляр бота
        chat_id: ID чата
        text_content: Текст с возможными тегами [BREAK]
        reply_to_message_id: ID сообщения для ответа
        user_id: ID пользователя
    
    Returns:
        ID последнего отправленного сообщения
    """
    import time
    import threading
    from bot_globals import multiple_messages_sending, multiple_messages_sending_lock, log_admin
    
    user_info_log = f"Utils send_multiple_messages (chat {chat_id})"
    
    # Проверяем наличие тега [BREAK]
    if '[BREAK]' not in text_content:
        # Если нет тега, отправляем как обычное сообщение
        return send_long_message(bot_instance, chat_id, text_content, reply_to_message_id, user_id=user_id)
    
    # Разделяем текст по тегу [BREAK]
    message_parts = text_content.split('[BREAK]')
    message_parts = [part.strip() for part in message_parts if part.strip()]
    
    if not message_parts:
        return None
    
    log_admin(f"{user_info_log}: Found {len(message_parts)} message parts to send", level="debug")
    
    last_message_id = None
    
    # Отправляем первое сообщение в реплай
    if message_parts[0]:
        try:
            first_msg = bot_instance.send_message(
                chat_id=chat_id,
                text=message_parts[0],
                reply_to_message_id=reply_to_message_id,
                parse_mode='HTML' if '<' in message_parts[0] else None
            )
            last_message_id = first_msg.message_id
            log_admin(f"{user_info_log}: Sent first message (reply) with ID {last_message_id}", level="debug")
        except Exception as e:
            log_admin(f"{user_info_log}: Error sending first message: {e}", level="error")
            return None
    
    # Отправляем остальные сообщения с задержкой в отдельном потоке
    if len(message_parts) > 1:
        # Устанавливаем блокировку чата чтобы не обрабатывать новые сообщения
        with multiple_messages_sending_lock:
            multiple_messages_sending[chat_id] = True
        
        log_admin(f"{user_info_log}: Set multiple messages lock for chat {chat_id}", level="debug")
        
        def send_delayed_messages():
            try:
                for i, part in enumerate(message_parts[1:], 1):
                    if not part:
                        continue
                    
                    # Рассчитываем задержку на основе длины сообщения (как у реального человека)
                    # Примерно 0.05 секунды на символ, минимум 1 секунда, максимум 5 секунд
                    delay = min(max(len(part) * 0.05, 1.0), 5.0)
                    time.sleep(delay)
                    
                    try:
                        msg = bot_instance.send_message(
                            chat_id=chat_id,
                            text=part,
                            parse_mode='HTML' if '<' in part else None
                        )
                        log_admin(f"{user_info_log}: Sent delayed message {i} with ID {msg.message_id} after {delay:.1f}s delay", level="debug")
                    except Exception as e:
                        log_admin(f"{user_info_log}: Error sending delayed message {i}: {e}", level="error")
            finally:
                # Снимаем блокировку после отправки всех сообщений
                with multiple_messages_sending_lock:
                    multiple_messages_sending.pop(chat_id, None)
                log_admin(f"{user_info_log}: Removed multiple messages lock for chat {chat_id}", level="debug")
                
                # Обрабатываем накопленные сообщения после небольшой задержки
                from bot_globals import process_pending_messages_for_chat
                process_pending_messages_for_chat(chat_id)
        
        # Запускаем отправку в отдельном потоке чтобы не блокировать основной поток
        thread = threading.Thread(target=send_delayed_messages, daemon=True)
        thread.start()
    
    return last_message_id


# MODIFIED send_long_message function starts here
def send_long_message(bot_instance, chat_id, text_content, reply_to_message_id, telegram_msg_limit=TELEGRAM_MSG_LIMIT, user_id=None): # initial_thinking_message_id removed
    """
    Sends a long message in chunks if it exceeds the Telegram message limit.
    Always sends messages as new, replying to reply_to_message_id.
    Always uses parse_mode='HTML'.
    Returns the message ID of the last sent chunk/message.

    Args:
        user_id: ID пользователя для определения групповых чатов (если None, обработка групп отключена)
    """
    last_sent_message_id = None
    user_info_log = f"Utils send_long_message (chat {chat_id})"

    # ЭТАП 3: Убран вызов process_text_for_group_chat - автосокращение отключено
    # Текст теперь отправляется без изменений во всех чатах

    user_requested_chunk_size = 3500
    user_requested_threshold = 4000
    default_effective_limit = telegram_msg_limit - 256 # Standard buffer

    current_limit = default_effective_limit
    if len(text_content) > user_requested_threshold:
        current_limit = user_requested_chunk_size
        log_admin(f"{user_info_log}: Text length ({len(text_content)}) > threshold ({user_requested_threshold}). Using user_requested_chunk_size: {user_requested_chunk_size}", level="debug")
    else:
        log_admin(f"{user_info_log}: Text length ({len(text_content)}) <= threshold ({user_requested_threshold}). Using default_effective_limit: {default_effective_limit}", level="debug")

    log_admin(f"{user_info_log}: Original telegram_msg_limit: {telegram_msg_limit}, Final current_limit for splitting: {current_limit}", level="debug")

    if not isinstance(text_content, str):
        log_admin(f"{user_info_log}: text_content was not a string, converting. Type: {type(text_content)}", level="warning")
        text_content = str(text_content)

    # Convert markdown bold to HTML tags before processing
    text_content = convert_markdown_to_html(text_content)
    # Fix HTML tags for Telegram compatibility before splitting or sending
    text_content = fix_telegram_html(text_content)
    # Validate and fix HTML tags to ensure they are properly closed
    text_content = validate_and_fix_html_tags(text_content)

    try:
        # Проверяем нужны ли кнопки для ответов ИИ (только для приватных чатов)
        is_private_chat = chat_id == user_id if user_id else False
        should_add_ai_response_buttons = is_private_chat and user_id is not None
        should_add_shortening_buttons = (
            is_private_chat and
            len(text_content) > 500 and
            user_id is not None
        )
        
        if len(text_content) > current_limit: # Use current_limit for decision to split
            log_admin(f"{user_info_log}: Message is long ({len(text_content)} chars). Splitting using smart HTML splitting. Limit used: {current_limit}", level="info")

            # Use smart HTML splitting instead of simple character-based splitting
            chunks = smart_split_html(text_content, current_limit)
            num_chunks = len(chunks)
            current_reply_to_id = reply_to_message_id # First chunk replies to the original message
            message_ids = []  # Храним ID всех отправленных сообщений

            log_admin(f"{user_info_log}: Smart HTML splitting created {num_chunks} chunks. Initial reply_to_id: {current_reply_to_id}", level="debug")

            # Log chunk sizes for debugging
            chunk_sizes = [len(chunk) for chunk in chunks]
            log_admin(f"{user_info_log}: Chunk sizes: {chunk_sizes}", level="debug")

            for i, chunk_text in enumerate(chunks):
                # Validate and fix HTML tags in chunk (additional safety)
                cleaned_chunk = validate_and_fix_html_tags(chunk_text.strip())
                log_admin(f"{user_info_log}: Processing chunk {i+1}/{num_chunks}. Length: {len(cleaned_chunk)}. Current reply_to_id for this chunk: {current_reply_to_id}", level="debug")

                # Определяем нужно ли добавить кнопку на последний чанк
                reply_markup = None
                if should_add_shortening_buttons and i == num_chunks - 1:  # Последний чанк
                    reply_markup = create_shortening_button(chat_id, user_id)

                # Always send as new message with thread safety
                sent_msg = safe_bot_call(
                    bot_instance.send_message,
                    chat_id,
                    cleaned_chunk,
                    reply_to_message_id=current_reply_to_id,
                    parse_mode='HTML',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )
                last_sent_message_id = sent_msg.message_id
                message_ids.append(last_sent_message_id)
                current_reply_to_id = last_sent_message_id # Subsequent chunks reply to the previous chunk from the bot
                log_admin(f"{user_info_log}: Sent chunk {i+1}/{num_chunks} as new message {last_sent_message_id}. current_reply_to_id set to: {current_reply_to_id}", level="info")
            
            # Сохраняем данные для сокращения если нужно
            if should_add_shortening_buttons and last_sent_message_id:
                store_ai_response_data(chat_id, last_sent_message_id, text_content, message_ids, user_id)
                
        else:
            # Validate and fix HTML tags in text
            cleaned_text = validate_and_fix_html_tags(text_content.strip())
            log_admin(f"{user_info_log}: Message not long ({len(text_content)} chars). Sending as single message.", level="info")
            
            # Определяем нужно ли добавить кнопку
            reply_markup = None
            if should_add_shortening_buttons:
                reply_markup = create_shortening_button(chat_id, user_id)
            
            # Always send as new message with thread safety
            sent_msg = safe_bot_call(
                bot_instance.send_message,
                chat_id,
                cleaned_text,
                reply_to_message_id=reply_to_message_id,
                parse_mode='HTML',
                reply_markup=reply_markup,
                disable_web_page_preview=True
            )
            last_sent_message_id = sent_msg.message_id
            
            # Сохраняем данные для сокращения если нужно
            if should_add_shortening_buttons and last_sent_message_id:
                store_ai_response_data(chat_id, last_sent_message_id, text_content, [last_sent_message_id], user_id)
                
            log_admin(f"{user_info_log}: Sent single message as new message {last_sent_message_id}.", level="info")

    except Exception as e_global_send_long:
        # Enhanced error logging
        error_message = f"{user_info_log}: Global error in send_long_message: {e_global_send_long}\n{traceback.format_exc()}"
        problematic_chunk_info = ""
        if 'chunk_text' in locals(): # If error happened during chunk processing
            problematic_chunk_info = f"\nProblematic chunk (original, before cleaning):\n---\n{chunk_text[:500]}...\n---"
        elif 'text_content' in locals() and len(text_content) <= current_limit : # Use current_limit for check
             problematic_chunk_info = f"\nProblematic message content (original, before cleaning):\n---\n{text_content[:500]}...\n---"

        # Check if it's a telebot API exception and log specifics if it's a parsing error
        if isinstance(e_global_send_long, telebot.apihelper.ApiTelegramException):
            error_message += f"\nTelebot API Exception: Code {e_global_send_long.error_code}, Result: {e_global_send_long.result_json}"
            if e_global_send_long.error_code == 400: # Bad Request
                error_message += f"\nDetected Bad Request (400). Potentially a parsing issue."
                if 'cleaned_chunk' in locals():
                    problematic_chunk_info += f"\nProblematic chunk (cleaned, as sent to API):\n---\n{cleaned_chunk[:500]}...\n---"
                elif 'cleaned_text' in locals():
                    problematic_chunk_info += f"\nProblematic message content (cleaned, as sent to API):\n---\n{cleaned_text[:500]}...\n---"

        log_admin(error_message + problematic_chunk_info, level="critical")

        if last_sent_message_id is None: # Only try to send an error if nothing was sent
            log_admin(f"{user_info_log}: Global error, no message part sent. Sending new error message.", level="warning")
            try:
                # Try to send a plain text error message with thread safety
                sent_err_msg = safe_bot_call(
                    bot_instance.send_message,
                    chat_id,
                    "⚠️ Ошибка: не удалось отправить ответ. Проблема с форматированием или длиной сообщения.",
                    reply_to_message_id=reply_to_message_id,
                    parse_mode=None
                )
                last_sent_message_id = sent_err_msg.message_id # Though this is an error msg ID
            except Exception as e_send_err_final:
                log_admin(f"{user_info_log}: Also failed to send a new error message: {e_send_err_final}", level="critical")

    log_admin(f"{user_info_log}: Exiting. Returning last_sent_message_id: {last_sent_message_id}", level="debug")
    return last_sent_message_id
# MODIFIED send_long_message function ends here

def _set_reaction_sync(bot_instance, chat_id: int, message_id: int, emoji_char: str):
    """
    Internal synchronous function to set reaction. Used by async wrapper.
    """
    from config import TELEGRAM_PALETTE_CUSTOM_ID

    user_info_log = f"Utils _set_reaction_sync (chat {chat_id}, msg {message_id}, emoji {emoji_char})"

    # 1) Special handling for palette emoji with custom ID support
    if emoji_char == "🎨" and TELEGRAM_PALETTE_CUSTOM_ID:
        try:
            if ReactionTypeEmoji:
                reaction_to_set = [ReactionTypeEmoji(custom_emoji_id=TELEGRAM_PALETTE_CUSTOM_ID)]
            else:
                # Fallback for older versions
                reaction_to_set = [{'type': 'custom_emoji', 'custom_emoji_id': TELEGRAM_PALETTE_CUSTOM_ID}]
        except Exception:
            # If custom emoji fails, fall back to safe emoji
            emoji_char = "🎉"
            reaction_to_set = [types.ReactionTypeEmoji(emoji_char)] if ReactionTypeEmoji else [{'type': 'emoji', 'emoji': emoji_char}]
    else:
        # 2) Safe emoji mapping for standard reactions
        # Only use emojis from Telegram's default reaction set
        safe_emojis = {"👍", "❤️", "😂", "😮", "😢", "😡", "🔥", "🎉", "👏", "🤔", "🙏", "🤯", "🤨", "👌", "⚡", "👨‍💻", "🍌"}
        safe_emoji = emoji_char if emoji_char in safe_emojis else "🎉"

        try:
            if ReactionTypeEmoji:
                reaction_to_set = [ReactionTypeEmoji(safe_emoji)]
            else:
                # Fallback for older versions
                reaction_to_set = [{'type': 'emoji', 'emoji': safe_emoji}]
        except AttributeError:
            # Final fallback for very old versions
            reaction_to_set = [{'type': 'emoji', 'emoji': safe_emoji}]

    # Use thread-safe wrapper for bot API call
    safe_bot_call(
        bot_instance.set_message_reaction,
        chat_id=chat_id,
        message_id=message_id,
        reaction=reaction_to_set,
        is_big=False  # Assuming standard size reactions
    )
    return True


def set_reaction(bot_instance, chat_id: int, message_id: int, emoji_char: str):
    """
    Sets a reaction to a specific message asynchronously with timeout protection.
    Supports custom emoji for palette (🎨) and safe fallbacks for invalid reactions.
    Uses thread pool to prevent blocking the main bot thread on timeouts.
    """
    from thread_pool_manager import submit_task_with_timeout
    from concurrent.futures import TimeoutError as FutureTimeoutError

    user_info_log = f"Utils set_reaction (chat {chat_id}, msg {message_id}, emoji {emoji_char})"

    try:
        log_admin(f"{user_info_log}: Attempting to set reaction asynchronously.", level="debug")

        # Submit task to thread pool with 8 second timeout (much shorter than default 30s)
        future = submit_task_with_timeout(
            _set_reaction_sync,
            8.0,  # 8 second timeout
            bot_instance,
            chat_id,
            message_id,
            emoji_char
        )

        # Wait for completion with timeout
        try:
            result = future.result(timeout=8.5)  # Slightly longer than task timeout
            log_admin(f"{user_info_log}: Successfully set reaction.", level="info")
            return result
        except FutureTimeoutError:
            log_admin(f"{user_info_log}: Reaction setting timed out after 8 seconds - continuing without blocking bot", level="warning")
            return False
        except Exception as e:
            # Handle API exceptions from the sync function
            if "REACTION_INVALID" in str(e):
                # Try fallback reaction asynchronously
                log_admin(f"{user_info_log}: REACTION_INVALID error, trying fallback emoji 🎉", level="warning")
                try:
                    fallback_future = submit_task_with_timeout(
                        _set_reaction_sync,
                        5.0,  # Shorter timeout for fallback
                        bot_instance,
                        chat_id,
                        message_id,
                        "🎉"  # Safe fallback emoji
                    )
                    fallback_result = fallback_future.result(timeout=5.5)
                    log_admin(f"{user_info_log}: Successfully set fallback reaction 🎉", level="info")
                    return fallback_result
                except (FutureTimeoutError, Exception) as fallback_error:
                    log_admin(f"{user_info_log}: Even fallback reaction failed/timed out: {fallback_error}", level="error")
                    return False
            else:
                log_admin(f"{user_info_log}: Failed to set reaction: {e}", level="error")
                return False

    except Exception as e:
        log_admin(f"{user_info_log}: Failed to set reaction: {e}\n{traceback.format_exc()}", level="error")
        return False

def _remove_reaction_sync(bot_instance, chat_id: int, message_id: int):
    """
    Internal synchronous function to remove reactions. Used by async wrapper.
    """
    # Passing an empty list removes reactions
    safe_bot_call(
        bot_instance.set_message_reaction,
        chat_id=chat_id,
        message_id=message_id,
        reaction=[],
        is_big=False # is_big is likely irrelevant when removing, but good to keep consistent
    )
    return True


def remove_reaction(bot_instance, chat_id: int, message_id: int):
    """
    Removes all reactions set by the bot from a specific message asynchronously with timeout protection.
    Uses thread pool to prevent blocking the main bot thread on timeouts.
    """
    from thread_pool_manager import submit_task_with_timeout
    from concurrent.futures import TimeoutError as FutureTimeoutError

    user_info_log = f"Utils remove_reaction (chat {chat_id}, msg {message_id})"

    try:
        log_admin(f"{user_info_log}: Attempting to remove reactions asynchronously.", level="debug")

        # Submit task to thread pool with 8 second timeout
        future = submit_task_with_timeout(
            _remove_reaction_sync,
            8.0,  # 8 second timeout
            bot_instance,
            chat_id,
            message_id
        )

        # Wait for completion with timeout
        try:
            result = future.result(timeout=8.5)  # Slightly longer than task timeout
            log_admin(f"{user_info_log}: Successfully sent request to remove reactions.", level="info")
            return result
        except FutureTimeoutError:
            log_admin(f"{user_info_log}: Remove reaction timed out after 8 seconds - continuing without blocking bot", level="warning")
            return False
        except Exception as e:
            log_admin(f"{user_info_log}: Failed to remove reactions: {e}", level="error")
            return False

    except Exception as e:
        log_admin(f"{user_info_log}: Failed to remove reactions: {e}\n{traceback.format_exc()}", level="error")
        return False

class TypingStatusManager:
    def __init__(self, bot_instance, chat_id):
        self.bot_instance = bot_instance
        self.chat_id = chat_id
        self.stop_event = threading.Event()
        self.thread = None
        self.active = False # To prevent multiple starts if not handled carefully

    def _keep_typing(self):
        log_admin_local = lambda msg, level="debug": log_admin(f"[TypingManager chat {self.chat_id}] {msg}", level=level)
        log_admin_local("Thread started.")
        while not self.stop_event.is_set():
            try:
                self.bot_instance.send_chat_action(self.chat_id, 'typing')
                # log_admin_local("Sent chat action 'typing'.") # Can be too verbose
            except Exception as e:
                log_admin_local(f"Error sending chat action: {e}", level="warning")
                # If send_chat_action fails (e.g., chat not found, bot blocked), stop trying.
                break
            self.stop_event.wait(2)  # Send every 2 seconds for more responsive typing indication
        log_admin_local("Thread finished.")

    def start(self):
        log_admin_local = lambda msg, level="debug": log_admin(f"[TypingManager chat {self.chat_id}] {msg}", level=level)
        if self.active:
            log_admin_local("Start called but already active.", level="warning")
            return
        self.stop_event.clear()
        self.thread = threading.Thread(target=self._keep_typing, daemon=True)
        self.thread.start()
        self.active = True
        log_admin_local("Typing status manager started.")

    def stop(self):
        log_admin_local = lambda msg, level="debug": log_admin(f"[TypingManager chat {self.chat_id}] {msg}", level=level)
        if not self.active:
            # log_admin_local("Stop called but not active.") # Can be verbose
            return
        self.stop_event.set()
        if self.thread and self.thread.is_alive():
            # log_admin_local("Waiting for typing thread to join...") # Verbose
            self.thread.join(timeout=5) # Wait a bit for thread to finish
            if self.thread.is_alive():
                log_admin_local("Typing thread did not join in time.", level="warning")
        self.active = False
        log_admin_local("Typing status manager stopped.")


def stop_instant_typing_if_any(bot_instance, chat_id, message_id):
    """
    Останавливает мгновенный typing менеджер, если он существует.
    Используется для очистки typing статуса при раннем завершении обработки.

    Args:
        bot_instance: Экземпляр бота
        chat_id: ID чата
        message_id: ID сообщения
    """
    if not hasattr(bot_instance, '_instant_typing_managers'):
        return

    key = f"{chat_id}_{message_id}"
    mgr = bot_instance._instant_typing_managers.pop(key, None)
    if mgr:
        try:
            mgr.stop()
            log_admin(f"Stopped instant typing manager for chat {chat_id}, message {message_id}")
        except Exception as e:
            log_admin(f"Error stopping instant typing manager for chat {chat_id}, message {message_id}: {e}")


def _combine_videos_ffmpeg(video_files, output_prefix):
    """
    Combines multiple video files into a single video using ffmpeg.

    Args:
        video_files: List of video data as bytes
        output_prefix: Prefix for temporary files

    Returns:
        Combined video data as bytes, or None on error
    """
    import tempfile
    import subprocess
    import uuid
    from bot_globals import log_admin

    if not video_files:
        log_admin("No video files to combine")
        return None

    if len(video_files) == 1:
        log_admin("Only one video file, returning as-is")
        return video_files[0]

    temp_dir = tempfile.gettempdir()
    temp_files = []
    concat_file_path = None
    output_file_path = None

    try:
        # Save all video files to temporary files
        for i, video_data in enumerate(video_files):
            if not isinstance(video_data, bytes) or len(video_data) == 0:
                log_admin(f"Invalid video data for file {i}")
                return None

            temp_filename = f"{output_prefix}_part_{i}_{uuid.uuid4().hex[:8]}.mp4"
            temp_filepath = os.path.join(temp_dir, temp_filename)

            with open(temp_filepath, 'wb') as f:
                f.write(video_data)

            temp_files.append(temp_filepath)
            log_admin(f"Saved video part {i+1} to {temp_filepath} ({len(video_data)} bytes)")

        # Create concat file for ffmpeg
        concat_filename = f"{output_prefix}_concat_{uuid.uuid4().hex[:8]}.txt"
        concat_file_path = os.path.join(temp_dir, concat_filename)

        with open(concat_file_path, 'w', encoding='utf-8') as f:
            for temp_file in temp_files:
                # Use forward slashes for ffmpeg compatibility
                normalized_path = temp_file.replace('\\', '/')
                f.write(f"file '{normalized_path}'\n")

        log_admin(f"Created concat file: {concat_file_path}")

        # Output file
        output_filename = f"{output_prefix}_combined_{uuid.uuid4().hex[:8]}.mp4"
        output_file_path = os.path.join(temp_dir, output_filename)

        # Run ffmpeg to combine videos
        ffmpeg_cmd = [
            'ffmpeg', '-y',  # -y to overwrite output file
            '-f', 'concat',
            '-safe', '0',
            '-i', concat_file_path,
            '-c', 'copy',  # Copy streams without re-encoding for speed
            output_file_path
        ]

        log_admin(f"Running ffmpeg command: {' '.join(ffmpeg_cmd)}")

        result = subprocess.run(
            ffmpeg_cmd,
            capture_output=True,
            text=True,
            timeout=300  # 5 minute timeout
        )

        if result.returncode != 0:
            log_admin(f"FFmpeg failed with return code {result.returncode}")
            log_admin(f"FFmpeg stderr: {result.stderr}")
            return None

        # Read the combined video file
        if not os.path.exists(output_file_path):
            log_admin(f"Output file not created: {output_file_path}")
            return None

        with open(output_file_path, 'rb') as f:
            combined_video_data = f.read()

        log_admin(f"Successfully combined {len(video_files)} videos into {len(combined_video_data)} bytes")
        return combined_video_data

    except subprocess.TimeoutExpired:
        log_admin("FFmpeg timeout during video combination")
        return None
    except Exception as e:
        log_admin(f"Error combining videos: {e}")
        return None
    finally:
        # Clean up temporary files
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception as e:
                log_admin(f"Error removing temp file {temp_file}: {e}")

        if concat_file_path and os.path.exists(concat_file_path):
            try:
                os.remove(concat_file_path)
            except Exception as e:
                log_admin(f"Error removing concat file {concat_file_path}: {e}")

        if output_file_path and os.path.exists(output_file_path):
            try:
                os.remove(output_file_path)
            except Exception as e:
                log_admin(f"Error removing output file {output_file_path}: {e}")


def clean_first_line_tags(dialogue_text):
    """
    Removes tags from the first line of podcast dialogue.
    """
    if not dialogue_text:
        return dialogue_text

    lines = dialogue_text.split('\n')
    if not lines:
        return dialogue_text

    # Clean first line from any tags in square brackets
    import re
    first_line = lines[0]
    cleaned_first_line = re.sub(r'\[.*?\]', '', first_line)
    # Remove extra spaces and strip
    cleaned_first_line = re.sub(r'\s+', ' ', cleaned_first_line).strip()

    # Rebuild the dialogue with cleaned first line
    lines[0] = cleaned_first_line
    return '\n'.join(lines)


def remove_duplicate_dialogue_lines(dialogue_text):
    """
    Removes duplicate consecutive dialogue lines from podcast text.
    Detects and removes exact duplicates and similar lines that may be generated by AI.
    """
    if not dialogue_text or not isinstance(dialogue_text, str):
        return dialogue_text

    import re
    from difflib import SequenceMatcher

    lines = dialogue_text.split('\n')
    if len(lines) <= 1:
        return dialogue_text

    cleaned_lines = []

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            cleaned_lines.append(line)
            continue

        # Check if this line is a dialogue line (starts with Woman: or Man:)
        if not (line.startswith('Woman:') or line.startswith('Man:')):
            cleaned_lines.append(line)
            continue

        # Extract speaker and content
        speaker_match = re.match(r'^(Woman|Man):\s*(.*)$', line)
        if not speaker_match:
            cleaned_lines.append(line)
            continue

        current_speaker = speaker_match.group(1)
        current_content = speaker_match.group(2).strip()

        # Skip empty content
        if not current_content:
            continue

        # Check for duplicates in the last few lines
        is_duplicate = False

        # Look back at the last 5 lines to find duplicates
        for j in range(max(0, len(cleaned_lines) - 5), len(cleaned_lines)):
            if j >= len(cleaned_lines):
                continue

            prev_line = cleaned_lines[j].strip()
            if not prev_line or not (prev_line.startswith('Woman:') or prev_line.startswith('Man:')):
                continue

            prev_match = re.match(r'^(Woman|Man):\s*(.*)$', prev_line)
            if not prev_match:
                continue

            prev_speaker = prev_match.group(1)
            prev_content = prev_match.group(2).strip()

            # Check for exact duplicate
            if current_speaker == prev_speaker and current_content == prev_content:
                log_admin(f"Found exact duplicate dialogue line: {line[:50]}...", level="warning")
                is_duplicate = True
                break

            # Check for similar content (>90% similarity)
            if current_speaker == prev_speaker and len(current_content) > 10 and len(prev_content) > 10:
                similarity = SequenceMatcher(None, current_content.lower(), prev_content.lower()).ratio()
                if similarity > 0.9:
                    log_admin(f"Found similar dialogue line ({similarity:.2%}): {line[:50]}...", level="warning")
                    is_duplicate = True
                    break

        if not is_duplicate:
            cleaned_lines.append(line)

    result = '\n'.join(cleaned_lines)

    # Log if we removed duplicates
    original_line_count = len([l for l in lines if l.strip() and (l.strip().startswith('Woman:') or l.strip().startswith('Man:'))])
    cleaned_line_count = len([l for l in cleaned_lines if l.strip() and (l.strip().startswith('Woman:') or l.strip().startswith('Man:'))])

    if original_line_count != cleaned_line_count:
        log_admin(f"Removed {original_line_count - cleaned_line_count} duplicate dialogue lines (from {original_line_count} to {cleaned_line_count})", level="info")

    return result


# --- Streaming Message Manager ---
class StreamingMessageManager:
    """
    Класс для управления стриминговыми сообщениями в Telegram.
    Обеспечивает плавное обновление сообщений по мере поступления токенов от ИИ.
    """

    def __init__(self, bot_instance, chat_id, reply_to_message_id=None):
        self.bot = bot_instance
        self.chat_id = chat_id
        self.reply_to_message_id = reply_to_message_id
        self.message_id = None
        self.current_text = ""
        self.last_update_time = 0
        self.update_interval = 0.75  # Уменьшаем интервал для более отзывчивого обновления
        self.min_chars_for_update = 5   # Минимальное кол-во символов для последующих обновлений
        self.is_first_update_sent = False  # Флаг для отслеживания первого обновления
        self.typing_indicator = "▌"
        self.is_finished = False
        self.buffer = ""
        self.lock = threading.Lock()

        # Логирование
        self.log_prefix = f"[StreamingManager/Chat{chat_id}]"
        log_admin(f"{self.log_prefix} Initialized streaming message manager")

    def start_stream(self, initial_text=""):
        """
        Начинает стриминг, отправляя первое сообщение.
        """
        with self.lock:
            if self.message_id:
                log_admin(f"{self.log_prefix} Stream already started, ignoring", level="warning")
                return

            try:
                # Отправляем первое сообщение с индикатором печати
                display_text = (initial_text + self.typing_indicator).strip()
                if not display_text:
                    display_text = self.typing_indicator

                message = self.bot.send_message(
                    chat_id=self.chat_id,
                    text=display_text,
                    reply_to_message_id=self.reply_to_message_id,
                    parse_mode='HTML',
                    disable_web_page_preview=True
                )

                self.message_id = message.message_id
                self.current_text = initial_text
                self.last_update_time = time.time()

                log_admin(f"{self.log_prefix} Started stream with message ID {self.message_id}")

            except Exception as e:
                log_admin(f"{self.log_prefix} Error starting stream: {e}", level="error")
                raise

    def update_stream(self, new_text_chunk):
        """
        Обновляет стриминговое сообщение новым чанком текста.
        """
        with self.lock:
            if self.is_finished:
                return

            # Проверяем, что чанк не пустой
            if not new_text_chunk or not isinstance(new_text_chunk, str):
                return

            if not self.message_id:
                # Если стрим еще не начат, начинаем его без текста
                self.start_stream()

            # Добавляем новый чанк в буфер
            self.buffer += new_text_chunk

            # Новая логика обновления
            current_time = time.time()
            time_since_update = current_time - self.last_update_time

            should_update = False
            # Первое обновление после 3 символов
            if not self.is_first_update_sent and len(self.current_text + self.buffer) >= 3:
                should_update = True
                self.is_first_update_sent = True
            # Последующие обновления каждые 5 символов
            elif self.is_first_update_sent and len(self.buffer) >= self.min_chars_for_update:
                should_update = True
            # Принудительное обновление по таймеру, чтобы не терять хвост
            elif self.is_first_update_sent and time_since_update >= self.update_interval and self.buffer:
                should_update = True

            if should_update:
                self._perform_update()

    def _perform_update(self):
        """
        Выполняет фактическое обновление сообщения в Telegram.
        """
        try:
            # Объединяем текущий текст с буфером
            new_full_text = self.current_text + self.buffer

            # Очищаем и валидируем HTML
            cleaned_text = validate_and_fix_html_tags(new_full_text)
            display_text = cleaned_text + self.typing_indicator

            # Обновляем сообщение
            self.bot.edit_message_text(
                text=display_text,
                chat_id=self.chat_id,
                message_id=self.message_id,
                parse_mode='HTML',
                disable_web_page_preview=True
            )

            # Обновляем состояние
            self.current_text = new_full_text
            self.buffer = ""
            self.last_update_time = time.time()

            log_admin(f"{self.log_prefix} Updated stream message (length: {len(self.current_text)})")

        except Exception as e:
            if "message is not modified" not in str(e).lower():
                log_admin(f"{self.log_prefix} Error updating stream: {e}", level="warning")

    def finish_stream(self, final_text_chunk="", user_id=None, enable_shortening=False):
        """
        Завершает стриминг, отправляя финальное сообщение без индикатора печати.
        
        Args:
            final_text_chunk: Финальный текст для добавления
            user_id: ID пользователя (для приватных чатов)
            enable_shortening: Добавить ли кнопки сокращения
        """
        with self.lock:
            if self.is_finished:
                return

            self.is_finished = True

            if not self.message_id:
                # Если стрим не был начат, просто отправляем обычное сообщение
                if final_text_chunk.strip():
                    try:
                        # Определяем нужны ли кнопки сокращения
                        reply_markup = None
                        if enable_shortening and user_id and self.chat_id == user_id and len(final_text_chunk) > 500:
                            reply_markup = create_shortening_button(self.chat_id, user_id)
                        
                        message = self.bot.send_message(
                            chat_id=self.chat_id,
                            text=validate_and_fix_html_tags(final_text_chunk),
                            reply_to_message_id=self.reply_to_message_id,
                            parse_mode='HTML',
                            reply_markup=reply_markup,
                            disable_web_page_preview=True
                        )
                        self.message_id = message.message_id
                        
                        # Сохраняем данные для сокращения если нужно
                        if reply_markup and self.message_id:
                            store_ai_response_data(self.chat_id, self.message_id, final_text_chunk, [self.message_id], user_id)
                        
                        log_admin(f"{self.log_prefix} Sent final message without streaming")
                    except Exception as e:
                        log_admin(f"{self.log_prefix} Error sending final message: {e}", level="error")
                        raise
                return self.message_id

            try:
                # Принудительно обновляем буфер перед завершением
                if self.buffer or final_text_chunk:
                    self._perform_update()

                # Добавляем финальный чанк и убираем индикатор печати
                final_full_text = self.current_text + self.buffer + final_text_chunk
                cleaned_final_text = validate_and_fix_html_tags(final_full_text)

                # Определяем нужны ли кнопки сокращения
                reply_markup = None
                if enable_shortening and user_id and self.chat_id == user_id and len(final_full_text) > 500:
                    reply_markup = create_shortening_button(self.chat_id, user_id)

                # Финальное обновление без индикатора печати
                self.bot.edit_message_text(
                    text=cleaned_final_text,
                    chat_id=self.chat_id,
                    message_id=self.message_id,
                    parse_mode='HTML',
                    reply_markup=reply_markup,
                    disable_web_page_preview=True
                )
                
                # Сохраняем данные для сокращения если нужно
                if reply_markup and self.message_id:
                    store_ai_response_data(self.chat_id, self.message_id, final_full_text, [self.message_id], user_id)

                log_admin(f"{self.log_prefix} Finished stream (final length: {len(cleaned_final_text)})")

            except Exception as e:
                if "message is not modified" not in str(e).lower():
                    log_admin(f"{self.log_prefix} Error finishing stream: {e}", level="warning")

            return self.message_id

    def cancel_stream(self):
        """
        Отменяет стриминг, удаляя сообщение если оно было создано.
        """
        with self.lock:
            if self.is_finished:
                return

            self.is_finished = True

            if self.message_id:
                try:
                    # Удаляем сообщение со стримом
                    self.bot.delete_message(
                        chat_id=self.chat_id,
                        message_id=self.message_id
                    )
                    log_admin(f"{self.log_prefix} Cancelled stream and deleted message {self.message_id}")
                except Exception as e:
                    log_admin(f"{self.log_prefix} Error cancelling stream: {e}", level="warning")

                self.message_id = None

    def cancel_stream(self):
        """
        Отменяет стриминг в случае ошибки.
        """
        with self.lock:
            self.is_finished = True
            log_admin(f"{self.log_prefix} Stream cancelled")


def send_parsed_files(bot_instance, chat_id: int, files_to_send: list[dict],
                     reply_to_message_id: int = None, user_id: int = None):
    """
    Отправляет файлы в телеграм на основе распарсенных FILE тегов.

    Args:
        bot_instance: Экземпляр телеграм бота
        chat_id (int): ID чата для отправки
        files_to_send (list[dict]): Список файлов с ключами 'name', 'extension', 'content'
        reply_to_message_id (int, optional): ID сообщения для ответа
        user_id (int, optional): ID пользователя для логирования

    Returns:
        list[int]: Список ID отправленных сообщений с файлами
    """
    import io
    import time
    from bot_globals import log_admin

    if not files_to_send:
        return []

    user_info_log = f"user {user_id}" if user_id else f"chat {chat_id}"
    sent_message_ids = []

    log_admin(f"{user_info_log} - Sending {len(files_to_send)} parsed files")

    for i, file_data in enumerate(files_to_send):
        try:
            filename = file_data['name']
            extension = file_data['extension']
            content = file_data['content']

            # Создаем полное имя файла
            full_filename = f"{filename}.{extension}" if extension else filename

            # Проверяем, что контент не пустой
            if not content or content.strip() == "":
                log_admin(f"{user_info_log} - Skipping empty file '{full_filename}'", level="warning")
                # Отправляем сообщение о пустом файле
                try:
                    fallback_text = f"⚠️ Файл <b>{full_filename}</b> пустой и не может быть отправлен."
                    fallback_message = bot_instance.send_message(
                        chat_id=chat_id,
                        text=fallback_text,
                        parse_mode="HTML",
                        reply_to_message_id=reply_to_message_id if i == 0 else None
                    )
                    sent_message_ids.append(fallback_message.message_id)
                    log_admin(f"{user_info_log} - Sent empty file notification for '{full_filename}'")
                except Exception as fallback_error:
                    log_admin(f"{user_info_log} - Failed to send empty file notification for '{full_filename}': {fallback_error}", level="error")
                continue

            # Создаем файл в памяти
            file_bytes = content.encode('utf-8')
            file_buffer = io.BytesIO(file_bytes)
            file_buffer.name = full_filename

            # Определяем размер файла
            file_size = len(file_bytes)
            size_kb = file_size / 1024
            size_mb = size_kb / 1024

            if size_mb > 1:
                size_str = f"{size_mb:.2f} MB"
            elif size_kb > 1:
                size_str = f"{size_kb:.2f} KB"
            else:
                size_str = f"{file_size} bytes"

            # Отправляем файл без подписи
            try:
                sent_message = bot_instance.send_document(
                    chat_id=chat_id,
                    document=file_buffer,
                    visible_file_name=full_filename,
                    reply_to_message_id=reply_to_message_id if i == 0 else None  # Только первый файл в ответ
                )

                sent_message_ids.append(sent_message.message_id)
                log_admin(f"{user_info_log} - Successfully sent file '{full_filename}' (ID: {sent_message.message_id})")

            except Exception as send_error:
                error_str = str(send_error).lower()
                if "file must be non-empty" in error_str:
                    log_admin(f"{user_info_log} - File '{full_filename}' is empty (API error): {send_error}", level="error")
                    # Специальное сообщение для пустых файлов
                    try:
                        fallback_text = f"⚠️ Файл <b>{full_filename}</b> оказался пустым и не может быть отправлен."
                        fallback_message = bot_instance.send_message(
                            chat_id=chat_id,
                            text=fallback_text,
                            parse_mode="HTML",
                            reply_to_message_id=reply_to_message_id if i == 0 else None
                        )
                        sent_message_ids.append(fallback_message.message_id)
                        log_admin(f"{user_info_log} - Sent empty file notification for '{full_filename}'")
                    except Exception as fallback_error:
                        log_admin(f"{user_info_log} - Failed to send empty file notification for '{full_filename}': {fallback_error}", level="error")
                else:
                    log_admin(f"{user_info_log} - Error sending file '{full_filename}': {send_error}", level="error")

                    # Попытка отправить как обычное сообщение в случае ошибки
                    try:
                        fallback_text = f"❌ Не удалось отправить файл <b>{full_filename}</b>\n\n<code>{content[:1000]}{'...' if len(content) > 1000 else ''}</code>"
                        fallback_message = bot_instance.send_message(
                            chat_id=chat_id,
                            text=fallback_text,
                            parse_mode="HTML",
                            reply_to_message_id=reply_to_message_id if i == 0 else None
                        )
                        sent_message_ids.append(fallback_message.message_id)
                        log_admin(f"{user_info_log} - Sent fallback message for file '{full_filename}'")
                    except Exception as fallback_error:
                        log_admin(f"{user_info_log} - Failed to send fallback message for '{full_filename}': {fallback_error}", level="error")

            # Небольшая задержка между отправкой файлов
            if i < len(files_to_send) - 1:
                time.sleep(0.1)

        except Exception as process_error:
            log_admin(f"{user_info_log} - Error processing file {i+1}: {process_error}", level="error")

    log_admin(f"{user_info_log} - Completed sending files. Sent {len(sent_message_ids)} messages")
    return sent_message_ids


# --- AI Response Shortening System ---

def create_new_chat_button(user_id: int) -> types.InlineKeyboardMarkup:
    """
    Создает inline кнопку "💬 Новый чат" для ответов ИИ в личных сообщениях.

    Args:
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup с кнопкой "Новый чат"
    """
    markup = types.InlineKeyboardMarkup()
    callback_data = f"new_chat_{user_id}"
    new_chat_button = types.InlineKeyboardButton("💬 Новый чат", callback_data=callback_data)
    markup.add(new_chat_button)
    return markup


def create_shortening_button(chat_id: int, user_id: int) -> types.InlineKeyboardMarkup:
    """
    Создает inline кнопку "Сократить" для ответов ИИ.

    Args:
        chat_id: ID чата
        user_id: ID пользователя

    Returns:
        InlineKeyboardMarkup с кнопкой сокращения
    """
    markup = types.InlineKeyboardMarkup()
    callback_data = f"shorten_{chat_id}_{user_id}"
    shorten_button = types.InlineKeyboardButton("Сократить", callback_data=callback_data)
    markup.add(shorten_button)
    return markup


def create_ai_response_buttons(chat_id: int, user_id: int, include_shortening: bool = False) -> types.InlineKeyboardMarkup:
    """
    Создает inline кнопки для ответов ИИ в личных сообщениях.
    Всегда включает кнопку "Новый чат", опционально кнопку "Сократить".

    Args:
        chat_id: ID чата
        user_id: ID пользователя
        include_shortening: Включать ли кнопку "Сократить"

    Returns:
        InlineKeyboardMarkup с кнопками для ответов ИИ
    """
    markup = types.InlineKeyboardMarkup()

    # Кнопка "Новый чат" (всегда присутствует в личных сообщениях)
    new_chat_callback = f"new_chat_{user_id}"
    new_chat_button = types.InlineKeyboardButton("💬 Новый чат", callback_data=new_chat_callback)

    if include_shortening:
        # Кнопка "Сократить"
        shorten_callback = f"shorten_{chat_id}_{user_id}"
        shorten_button = types.InlineKeyboardButton("Сократить", callback_data=shorten_callback)
        # Добавляем кнопки в один ряд
        markup.row(new_chat_button, shorten_button)
    else:
        # Только кнопка "Новый чат"
        markup.add(new_chat_button)

    return markup


def create_restore_button(chat_id: int, user_id: int) -> types.InlineKeyboardMarkup:
    """
    Создает inline кнопку "Вернуть исходно" для восстановления оригинального текста.
    
    Args:
        chat_id: ID чата
        user_id: ID пользователя
        
    Returns:
        InlineKeyboardMarkup с кнопкой восстановления
    """
    markup = types.InlineKeyboardMarkup()
    callback_data = f"restore_{chat_id}_{user_id}"
    restore_button = types.InlineKeyboardButton("Вернуть исходно", callback_data=callback_data)
    markup.add(restore_button)
    return markup


def store_ai_response_data(chat_id: int, last_message_id: int, original_text: str, message_ids: list, user_id: int):
    """
    Сохраняет данные ответа ИИ для возможности сокращения/восстановления.
    
    Args:
        chat_id: ID чата
        last_message_id: ID последнего сообщения в последовательности
        original_text: Полный оригинальный текст
        message_ids: Список ID всех сообщений в последовательности
        user_id: ID пользователя
    """
    from bot_globals import ai_response_storage, ai_response_storage_lock
    
    storage_key = f"{chat_id}_{last_message_id}"
    
    with ai_response_storage_lock:
        ai_response_storage[storage_key] = {
            'original_text': original_text,
            'message_ids': message_ids,
            'user_id': user_id,
            'timestamp': time.time(),
            'shortened_text': None
        }
    
    log_admin(f"Stored AI response data for shortening: key={storage_key}, messages={len(message_ids)}, length={len(original_text)}", level="debug")


def get_ai_response_data(chat_id: int, message_id: int) -> dict:
    """
    Получает сохраненные данные ответа ИИ.
    
    Args:
        chat_id: ID чата
        message_id: ID сообщения
        
    Returns:
        Словарь с данными или None если не найдено
    """
    from bot_globals import ai_response_storage, ai_response_storage_lock
    
    storage_key = f"{chat_id}_{message_id}"
    
    with ai_response_storage_lock:
        return ai_response_storage.get(storage_key)


